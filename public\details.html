<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Account Details - OFCA Portal</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="css/ofca-styles.css">
    <script src="js/header.js"></script>
    <script src="js/footer.js"></script>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="p-8">
    <div class="bg-white p-8 rounded-lg shadow-lg w-full max-w-4xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold">User Account Details</h1>
            <a href="index.html" class="text-indigo-600 hover:text-indigo-900">Back to User List</a>
        </div>

        <div class="space-y-6">
            <!-- Personal Information Section -->
            <div>
                <h2 class="text-lg font-medium text-gray-900 border-b pb-2">Personal Information</h2>
                <div class="mt-4 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                    <div class="sm:col-span-3">
                        <p class="block text-sm font-medium text-gray-700">First Name</p>
                        <p class="mt-1 block w-full px-3 py-2 border border-gray-100 rounded-md shadow-sm bg-gray-50 sm:text-sm" id="firstName">Loading...</p>
                    </div>

                    <div class="sm:col-span-3">
                        <p class="block text-sm font-medium text-gray-700">Last Name</p>
                        <p class="mt-1 block w-full px-3 py-2 border border-gray-100 rounded-md shadow-sm bg-gray-50 sm:text-sm" id="lastName">Loading...</p>
                    </div>

                    <div class="sm:col-span-3">
                        <p class="block text-sm font-medium text-gray-700">Email Address</p>
                        <p class="mt-1 block w-full px-3 py-2 border border-gray-100 rounded-md shadow-sm bg-gray-50 sm:text-sm" id="email">Loading...</p>
                    </div>

                    <div class="sm:col-span-3">
                        <p class="block text-sm font-medium text-gray-700">Phone Number</p>
                        <p class="mt-1 block w-full px-3 py-2 border border-gray-100 rounded-md shadow-sm bg-gray-50 sm:text-sm" id="phone">Loading...</p>
                    </div>
                </div>
            </div>

            <!-- Employee Information Section -->
            <div>
                <h2 class="text-lg font-medium text-gray-900 border-b pb-2">Employee Information</h2>
                <div class="mt-4 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                    <div class="sm:col-span-3">
                        <p class="block text-sm font-medium text-gray-700">Start Date</p>
                        <p class="mt-1 block w-full px-3 py-2 border border-gray-100 rounded-md shadow-sm bg-gray-50 sm:text-sm" id="startDate">Loading...</p>
                    </div>

                    <div class="sm:col-span-3">
                        <p class="block text-sm font-medium text-gray-700">Position</p>
                        <p class="mt-1 block w-full px-3 py-2 border border-gray-100 rounded-md shadow-sm bg-gray-50 sm:text-sm" id="position">Loading...</p>
                    </div>
                </div>
            </div>

            <!-- Rank Section -->
            <div>
                <h2 class="text-lg font-medium text-gray-900 border-b pb-2">Rank Information</h2>
                <div class="mt-4 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                    <div class="sm:col-span-3">
                        <p class="block text-sm font-medium text-gray-700">Rank Identifier</p>
                        <p class="mt-1 block w-full px-3 py-2 border border-gray-100 rounded-md shadow-sm bg-gray-50 sm:text-sm" id="rankIdentifier">Loading...</p>
                    </div>

                    <div class="sm:col-span-3">
                        <p class="block text-sm font-medium text-gray-700">Rank Name</p>
                        <p class="mt-1 block w-full px-3 py-2 border border-gray-100 rounded-md shadow-sm bg-gray-50 sm:text-sm" id="rankName">Loading...</p>
                    </div>

                    <div class="sm:col-span-3">
                        <p class="block text-sm font-medium text-gray-700">Branch</p>
                        <p class="mt-1 block w-full px-3 py-2 border border-gray-100 rounded-md shadow-sm bg-gray-50 sm:text-sm" id="branch">Loading...</p>
                    </div>

                    <div class="sm:col-span-3">
                        <p class="block text-sm font-medium text-gray-700">Division</p>
                        <p class="mt-1 block w-full px-3 py-2 border border-gray-100 rounded-md shadow-sm bg-gray-50 sm:text-sm" id="division">Loading...</p>
                    </div>

                    <div class="sm:col-span-3">
                        <p class="block text-sm font-medium text-gray-700">Section</p>
                        <p class="mt-1 block w-full px-3 py-2 border border-gray-100 rounded-md shadow-sm bg-gray-50 sm:text-sm" id="section">Loading...</p>
                    </div>

                    <div class="sm:col-span-3">
                        <p class="block text-sm font-medium text-gray-700">Team</p>
                        <p class="mt-1 block w-full px-3 py-2 border border-gray-100 rounded-md shadow-sm bg-gray-50 sm:text-sm" id="team">Loading...</p>
                    </div>

                    <div class="sm:col-span-3">
                        <p class="block text-sm font-medium text-gray-700">Rank Supervisor</p>
                        <p class="mt-1 block w-full px-3 py-2 border border-gray-100 rounded-md shadow-sm bg-gray-50 sm:text-sm" id="rankSupervisor">Loading...</p>
                    </div>
                </div>
            </div>

            <!-- Account Details Section -->
            <div>
                <h2 class="text-lg font-medium text-gray-900 border-b pb-2">Account Details</h2>
                <div class="mt-4 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                    <div class="sm:col-span-3">
                        <p class="block text-sm font-medium text-gray-700">Username</p>
                        <p class="mt-1 block w-full px-3 py-2 border border-gray-100 rounded-md shadow-sm bg-gray-50 sm:text-sm" id="username">Loading...</p>
                    </div>

                    <div class="sm:col-span-3">
                        <p class="block text-sm font-medium text-gray-700">Status</p>
                        <p class="mt-1 block w-full px-3 py-2 border border-gray-100 rounded-md shadow-sm bg-gray-50 sm:text-sm" id="status">Loading...</p>
                    </div>

                    <div class="sm:col-span-3">
                        <p class="block text-sm font-medium text-gray-700">Access Level</p>
                        <p class="mt-1 block w-full px-3 py-2 border border-gray-100 rounded-md shadow-sm bg-gray-50 sm:text-sm" id="accessLevel">Loading...</p>
                    </div>

                    <div class="sm:col-span-6">
                        <p class="block text-sm font-medium text-gray-700">Additional Notes</p>
                        <p class="mt-1 block w-full px-3 py-2 border border-gray-100 rounded-md shadow-sm bg-gray-50 sm:text-sm min-h-[72px]" id="notes">Loading...</p>
                    </div>
                </div>
            </div>

            <!-- Personnel History Section -->
            <div>
                <h2 class="text-lg font-medium text-gray-900 border-b pb-2">Personnel History</h2>
                <div class="mt-4">
                    <!-- Filter Controls -->
                    <div class="mb-4 flex flex-wrap items-end space-x-4">
                        <div>
                            <label for="historyStartDate" class="block text-sm font-medium text-gray-700">From Date</label>
                            <input type="date" id="historyStartDate" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                        </div>
                        <div>
                            <label for="historyEndDate" class="block text-sm font-medium text-gray-700">To Date</label>
                            <input type="date" id="historyEndDate" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                        </div>
                        <div>
                            <label for="eventTypeFilter" class="block text-sm font-medium text-gray-700">Event Type</label>
                            <select id="eventTypeFilter" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                <option value="all">All Events</option>
                                <option value="promotion">Promotions</option>
                                <option value="transfer">Transfers</option>
                                <option value="newPosition">New Position</option>
                            </select>
                        </div>
                        <div>
                            <button id="applyHistoryFilter" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                Search
                            </button>
                        </div>
                    </div>

                    <!-- History Table -->
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Event Type</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Position</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Details</th>
                                </tr>
                            </thead>
                            <tbody id="personnelHistoryTable" class="bg-white divide-y divide-gray-200">
                                <!-- History entries will be populated by JavaScript -->
                                <tr>
                                    <td colspan="4" class="px-6 py-4 text-center text-sm text-gray-500">No history records found.</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <div class="flex justify-end space-x-3">
                <a href="index.html" class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Back to List
                </a>

                <a href="modify-account.html?user=" id="modifyLink" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Modify Account
                </a>
            </div>
        </div>
    </div>

    <script>
        // Get users from localStorage
        const users = JSON.parse(localStorage.getItem('users')) || [];

        // Get user from query parameter
        const urlParams = new URLSearchParams(window.location.search);
        const username = urlParams.get('user');
        const user = users.find(u => u.username === username);

        // Set the modify link
        document.getElementById('modifyLink').href = `modify-account.html?user=${username}`;

        // Function to display user data
        function displayUserData(userData) {
            // Personal Information
            document.getElementById('firstName').textContent = userData.firstName || 'Not provided';
            document.getElementById('lastName').textContent = userData.lastName || 'Not provided';
            document.getElementById('email').textContent = userData.email || 'Not provided';
            document.getElementById('phone').textContent = userData.phone || 'Not provided';

            // Employee Information
            document.getElementById('startDate').textContent = userData.startDate || 'Not provided';
            document.getElementById('position').textContent = userData.position || 'Not provided';

            // Rank Information
            document.getElementById('rankIdentifier').textContent = userData.rankIdentifier || 'Not provided';
            document.getElementById('rankName').textContent = userData.rankName || 'Not provided';
            document.getElementById('branch').textContent = userData.branch || 'Not provided';
            document.getElementById('division').textContent = userData.division || 'Not provided';
            document.getElementById('section').textContent = userData.section || 'Not provided';
            document.getElementById('team').textContent = userData.team || 'Not provided';
            document.getElementById('rankSupervisor').textContent = userData.rankSupervisor || 'Not provided';

            // Account Details
            document.getElementById('username').textContent = userData.username || 'Not provided';

            // Capitalize first letter of status for display
            const status = userData.status || 'Not provided';
            document.getElementById('status').textContent = status.charAt(0).toUpperCase() + status.slice(1);

            document.getElementById('accessLevel').textContent = userData.accessLevel || 'Not provided';
            document.getElementById('notes').textContent = userData.notes || 'No additional notes';
        }

        // Function to display personnel history
        function displayPersonnelHistory(userData) {
            // Initialize personnel history if it doesn't exist
            if (!userData.personnelHistory) {
                // Create empty history array (no default entries)
                userData.personnelHistory = [];

                // Save updated user data
                const userIndex = users.findIndex(u => u.username === userData.username);
                if (userIndex !== -1) {
                    users[userIndex] = userData;
                    localStorage.setItem('users', JSON.stringify(users));
                }
            }

            // Populate history table
            populateHistoryTable(userData.personnelHistory);

            // Set up filter functionality
            document.getElementById('applyHistoryFilter').addEventListener('click', function() {
                filterPersonnelHistory(userData.personnelHistory);
            });
        }

        // Function to populate history table
        function populateHistoryTable(historyData) {
            const tableBody = document.getElementById('personnelHistoryTable');

            if (!historyData || historyData.length === 0) {
                tableBody.innerHTML = `
                    <tr>
                        <td colspan="4" class="px-6 py-4 text-center text-sm text-gray-500">No history records found.</td>
                    </tr>
                `;
                return;
            }

            // Sort history by date (newest first)
            const sortedHistory = [...historyData].sort((a, b) => new Date(b.date) - new Date(a.date));

            // Clear table
            tableBody.innerHTML = '';

            // Add rows
            sortedHistory.forEach(entry => {
                const row = document.createElement('tr');

                // Format date
                const dateObj = new Date(entry.date);
                const formattedDate = dateObj.toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric'
                });

                // Create cells
                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${formattedDate}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${entry.eventType === 'promotion' ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'}">
                            ${entry.eventType.charAt(0).toUpperCase() + entry.eventType.slice(1)}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${entry.position}</td>
                    <td class="px-6 py-4 text-sm text-gray-900">${entry.details}</td>
                `;

                tableBody.appendChild(row);
            });
        }

        // Function to filter personnel history
        function filterPersonnelHistory(historyData) {
            if (!historyData || historyData.length === 0) return;

            const startDate = document.getElementById('historyStartDate').value;
            const endDate = document.getElementById('historyEndDate').value;
            const eventType = document.getElementById('eventTypeFilter').value;

            // Filter history based on criteria
            let filteredHistory = [...historyData];

            // Filter by date range
            if (startDate) {
                filteredHistory = filteredHistory.filter(entry => entry.date >= startDate);
            }

            if (endDate) {
                filteredHistory = filteredHistory.filter(entry => entry.date <= endDate);
            }

            // Filter by event type
            if (eventType && eventType !== 'all') {
                filteredHistory = filteredHistory.filter(entry => entry.eventType === eventType);
            }

            // Update table with filtered data
            populateHistoryTable(filteredHistory);
        }

        // Display user details or show error
        if (user) {
            displayUserData(user);
            displayPersonnelHistory(user);
        } else {
            // Check for default users if not found in localStorage
            const defaultUsers = [
                {
                    id: '1',
                    firstName: 'John',
                    lastName: 'Doe',
                    username: 'jdoe',
                    email: '<EMAIL>',
                    phone: '(*************',
                    department: 'IT',
                    jobTitle: 'Software Engineer',
                    role: 'Admin',
                    status: 'active',
                    accessLevel: 'Full',
                    startDate: '2022-01-15',
                    notes: 'System administrator with full access privileges.'
                },
                {
                    id: '2',
                    firstName: 'Jane',
                    lastName: 'Smith',
                    username: 'jsmith',
                    email: '<EMAIL>',
                    phone: '(*************',
                    department: 'HR',
                    jobTitle: 'HR Manager',
                    role: 'User',
                    status: 'pending',
                    accessLevel: 'Standard',
                    startDate: '2022-03-10',
                    notes: 'Handles employee onboarding and HR processes.'
                },
                {
                    id: '3',
                    firstName: 'Alice',
                    lastName: 'Johnson',
                    username: 'ajohnson',
                    email: '<EMAIL>',
                    phone: '(*************',
                    department: 'Finance',
                    jobTitle: 'Accountant',
                    role: 'User',
                    status: 'resigned',
                    accessLevel: 'Basic',
                    startDate: '2021-11-05',
                    notes: 'Former employee, account kept for record purposes.'
                },
                {
                    id: '4',
                    firstName: 'Robert',
                    lastName: 'Williams',
                    username: 'rwilliams',
                    email: '<EMAIL>',
                    phone: '(*************',
                    department: 'IT',
                    jobTitle: 'Network Engineer',
                    role: 'IT Officer',
                    status: 'disabled',
                    accessLevel: 'Advanced',
                    startDate: '2022-02-20',
                    notes: 'Account temporarily disabled due to extended leave.'
                }
            ];

            const defaultUser = defaultUsers.find(u => u.username === username);

            if (defaultUser) {
                displayUserData(defaultUser);
            } else {
                // Show error message if user not found
                const sections = document.querySelectorAll('.space-y-6 > div');
                sections.forEach(section => {
                    const grid = section.querySelector('.grid');
                    if (grid) {
                        grid.innerHTML = '<p class="col-span-6 text-red-600 py-4">User not found.</p>';
                    }
                });
            }
        }
    </script>
</body>
</html>

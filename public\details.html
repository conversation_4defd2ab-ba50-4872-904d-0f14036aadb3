<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Account Details - OFCA Portal</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="css/ofca-styles.css">
    <script src="js/header.js"></script>
    <script src="js/footer.js"></script>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="p-8">
    <div class="bg-white p-8 rounded-lg shadow-lg w-full max-w-4xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold">User Account Details</h1>
            <a href="index.html" class="text-indigo-600 hover:text-indigo-900">Back to User List</a>
        </div>

        <div class="space-y-6">
            <!-- Personal Information Section -->
            <div>
                <h2 class="text-lg font-medium text-gray-900 border-b pb-2">Personal Information</h2>
                <div class="mt-4 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                    <div class="sm:col-span-3">
                        <p class="block text-sm font-medium text-gray-700">First Name</p>
                        <p class="mt-1 block w-full px-3 py-2 border border-gray-100 rounded-md shadow-sm bg-gray-50 sm:text-sm" id="firstName">Loading...</p>
                    </div>

                    <div class="sm:col-span-3">
                        <p class="block text-sm font-medium text-gray-700">Last Name</p>
                        <p class="mt-1 block w-full px-3 py-2 border border-gray-100 rounded-md shadow-sm bg-gray-50 sm:text-sm" id="lastName">Loading...</p>
                    </div>

                    <div class="sm:col-span-3">
                        <p class="block text-sm font-medium text-gray-700">Email Address</p>
                        <p class="mt-1 block w-full px-3 py-2 border border-gray-100 rounded-md shadow-sm bg-gray-50 sm:text-sm" id="email">Loading...</p>
                    </div>

                    <div class="sm:col-span-3">
                        <p class="block text-sm font-medium text-gray-700">Phone Number</p>
                        <p class="mt-1 block w-full px-3 py-2 border border-gray-100 rounded-md shadow-sm bg-gray-50 sm:text-sm" id="phone">Loading...</p>
                    </div>
                </div>
            </div>

            <!-- Rank Section -->
            <div>
                <h2 class="text-lg font-medium text-gray-900 border-b pb-2">Rank Information</h2>
                <div class="mt-4 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                    <div class="sm:col-span-3">
                        <p class="block text-sm font-medium text-gray-700">Rank Identifier</p>
                        <p class="mt-1 block w-full px-3 py-2 border border-gray-100 rounded-md shadow-sm bg-gray-50 sm:text-sm" id="rankIdentifier">Loading...</p>
                    </div>

                    <div class="sm:col-span-3">
                        <p class="block text-sm font-medium text-gray-700">Rank Name</p>
                        <p class="mt-1 block w-full px-3 py-2 border border-gray-100 rounded-md shadow-sm bg-gray-50 sm:text-sm" id="rankName">Loading...</p>
                    </div>

                    <div class="sm:col-span-3">
                        <p class="block text-sm font-medium text-gray-700">Branch</p>
                        <p class="mt-1 block w-full px-3 py-2 border border-gray-100 rounded-md shadow-sm bg-gray-50 sm:text-sm" id="branch">Loading...</p>
                    </div>

                    <div class="sm:col-span-3">
                        <p class="block text-sm font-medium text-gray-700">Division</p>
                        <p class="mt-1 block w-full px-3 py-2 border border-gray-100 rounded-md shadow-sm bg-gray-50 sm:text-sm" id="division">Loading...</p>
                    </div>

                    <div class="sm:col-span-3">
                        <p class="block text-sm font-medium text-gray-700">Section</p>
                        <p class="mt-1 block w-full px-3 py-2 border border-gray-100 rounded-md shadow-sm bg-gray-50 sm:text-sm" id="section">Loading...</p>
                    </div>

                    <div class="sm:col-span-3">
                        <p class="block text-sm font-medium text-gray-700">Team</p>
                        <p class="mt-1 block w-full px-3 py-2 border border-gray-100 rounded-md shadow-sm bg-gray-50 sm:text-sm" id="team">Loading...</p>
                    </div>
                </div>
            </div>

            <!-- Account Details Section -->
            <div>
                <h2 class="text-lg font-medium text-gray-900 border-b pb-2">Account Details</h2>
                <div class="mt-4 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                    <div class="sm:col-span-3">
                        <p class="block text-sm font-medium text-gray-700">Username</p>
                        <p class="mt-1 block w-full px-3 py-2 border border-gray-100 rounded-md shadow-sm bg-gray-50 sm:text-sm" id="username">Loading...</p>
                    </div>

                    <div class="sm:col-span-3">
                        <p class="block text-sm font-medium text-gray-700">Status</p>
                        <p class="mt-1 block w-full px-3 py-2 border border-gray-100 rounded-md shadow-sm bg-gray-50 sm:text-sm" id="status">Loading...</p>
                    </div>

                    <div class="sm:col-span-3">
                        <p class="block text-sm font-medium text-gray-700">Access Level</p>
                        <p class="mt-1 block w-full px-3 py-2 border border-gray-100 rounded-md shadow-sm bg-gray-50 sm:text-sm" id="accessLevel">Loading...</p>
                    </div>

                    <div class="sm:col-span-6">
                        <p class="block text-sm font-medium text-gray-700">Additional Notes</p>
                        <p class="mt-1 block w-full px-3 py-2 border border-gray-100 rounded-md shadow-sm bg-gray-50 sm:text-sm min-h-[72px]" id="notes">Loading...</p>
                    </div>
                </div>
            </div>

            <div class="flex justify-end space-x-3">
                <a href="index.html" class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Back to List
                </a>

                <a href="modify-account.html?user=" id="modifyLink" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Modify Account
                </a>
            </div>
        </div>
    </div>

    <script>
        // Get users from localStorage
        const users = JSON.parse(localStorage.getItem('users')) || [];

        // Get user from query parameter
        const urlParams = new URLSearchParams(window.location.search);
        const username = urlParams.get('user');
        const user = users.find(u => u.username === username);

        // Set the modify link
        document.getElementById('modifyLink').href = `modify-account.html?user=${username}`;

        // Function to display user data
        function displayUserData(userData) {
            // Personal Information
            document.getElementById('firstName').textContent = userData.firstName || 'Not provided';
            document.getElementById('lastName').textContent = userData.lastName || 'Not provided';
            document.getElementById('email').textContent = userData.email || 'Not provided';
            document.getElementById('phone').textContent = userData.phone || 'Not provided';

            // Rank Information
            document.getElementById('rankIdentifier').textContent = userData.rankIdentifier || 'Not provided';
            document.getElementById('rankName').textContent = userData.rankName || 'Not provided';
            document.getElementById('branch').textContent = userData.branch || 'Not provided';
            document.getElementById('division').textContent = userData.division || 'Not provided';
            document.getElementById('section').textContent = userData.section || 'Not provided';
            document.getElementById('team').textContent = userData.team || 'Not provided';

            // Account Details
            document.getElementById('username').textContent = userData.username || 'Not provided';

            // Capitalize first letter of status for display
            const status = userData.status || 'Not provided';
            document.getElementById('status').textContent = status.charAt(0).toUpperCase() + status.slice(1);

            document.getElementById('accessLevel').textContent = userData.accessLevel || 'Not provided';
            document.getElementById('notes').textContent = userData.notes || 'No additional notes';
        }

        // Display user details or show error
        if (user) {
            displayUserData(user);
        } else {
            // Check for default users if not found in localStorage
            const defaultUsers = [
                {
                    id: '1',
                    firstName: 'John',
                    lastName: 'Doe',
                    username: 'jdoe',
                    email: '<EMAIL>',
                    phone: '(*************',
                    department: 'IT',
                    jobTitle: 'Software Engineer',
                    role: 'Admin',
                    status: 'active',
                    accessLevel: 'Full',
                    startDate: '2022-01-15',
                    notes: 'System administrator with full access privileges.'
                },
                {
                    id: '2',
                    firstName: 'Jane',
                    lastName: 'Smith',
                    username: 'jsmith',
                    email: '<EMAIL>',
                    phone: '(*************',
                    department: 'HR',
                    jobTitle: 'HR Manager',
                    role: 'User',
                    status: 'pending',
                    accessLevel: 'Standard',
                    startDate: '2022-03-10',
                    notes: 'Handles employee onboarding and HR processes.'
                },
                {
                    id: '3',
                    firstName: 'Alice',
                    lastName: 'Johnson',
                    username: 'ajohnson',
                    email: '<EMAIL>',
                    phone: '(*************',
                    department: 'Finance',
                    jobTitle: 'Accountant',
                    role: 'User',
                    status: 'resigned',
                    accessLevel: 'Basic',
                    startDate: '2021-11-05',
                    notes: 'Former employee, account kept for record purposes.'
                },
                {
                    id: '4',
                    firstName: 'Robert',
                    lastName: 'Williams',
                    username: 'rwilliams',
                    email: '<EMAIL>',
                    phone: '(*************',
                    department: 'IT',
                    jobTitle: 'Network Engineer',
                    role: 'IT Officer',
                    status: 'disabled',
                    accessLevel: 'Advanced',
                    startDate: '2022-02-20',
                    notes: 'Account temporarily disabled due to extended leave.'
                }
            ];

            const defaultUser = defaultUsers.find(u => u.username === username);

            if (defaultUser) {
                displayUserData(defaultUser);
            } else {
                // Show error message if user not found
                const sections = document.querySelectorAll('.space-y-6 > div');
                sections.forEach(section => {
                    const grid = section.querySelector('.grid');
                    if (grid) {
                        grid.innerHTML = '<p class="col-span-6 text-red-600 py-4">User not found.</p>';
                    }
                });
            }
        }
    </script>
</body>
</html>

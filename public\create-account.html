<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create User Account - OFCA Portal</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="css/ofca-styles.css">
    <script src="js/header.js"></script>
    <script src="js/footer.js"></script>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="p-8">
    <div class="bg-white p-8 rounded-lg shadow-lg w-full max-w-4xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold">User Account Management</h1>
            <a href="index.html" class="text-indigo-600 hover:text-indigo-900">Back to User List</a>
        </div>

        <!-- Tab Navigation -->
        <div class="border-b border-gray-200 mb-6">
            <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                <button id="createAccountTab" class="border-indigo-500 text-indigo-600 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" onclick="showTab('createAccount')">
                    Create Account
                </button>
                <button id="pendingAccountsTab" class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" onclick="showTab('pendingAccounts')">
                    Pending Account Creation
                </button>
            </nav>
        </div>

        <!-- Create Account Tab Content -->
        <div id="createAccountContent">
            <form id="createAccountForm" class="space-y-6">
            <!-- Personal Information Section -->
            <div>
                <h2 class="text-lg font-medium text-gray-900 border-b pb-2">Personal Information</h2>
                <div class="mt-4 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                    <div class="sm:col-span-3">
                        <label for="firstName" class="block text-sm font-medium text-gray-700">First Name</label>
                        <input type="text" name="firstName" id="firstName" required class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>

                    <div class="sm:col-span-3">
                        <label for="lastName" class="block text-sm font-medium text-gray-700">Last Name</label>
                        <input type="text" name="lastName" id="lastName" required class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>


                     <div class="sm:col-span-3">
                        <label for="email" class="block text-sm font-medium text-gray-700">Email Address</label>
                        <input type="email" name="email" id="email" required class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>


                    <div class="sm:col-span-3">
                        <label for="phone" class="block text-sm font-medium text-gray-700">Phone Number</label>
                        <input type="tel" name="phone" id="phone" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>
                </div>
            </div>

            <!-- Employment Information Section -->
            <div>
                <h2 class="text-lg font-medium text-gray-900 border-b pb-2">Employment Information</h2>
                <div class="mt-4 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                    <div class="sm:col-span-3">
                        <label for="employeeId" class="block text-sm font-medium text-gray-700">Employee ID</label>
                        <input type="text" name="employeeId" id="employeeId" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>

                    <div class="sm:col-span-3">
                        <label for="dateOfJoin" class="block text-sm font-medium text-gray-700">Date of Join</label>
                        <input type="date" name="dateOfJoin" id="dateOfJoin" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>

                    <div class="sm:col-span-3">
                        <label for="position" class="block text-sm font-medium text-gray-700">Position</label>
                        <input type="text" name="position" id="position" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>

                    <div class="sm:col-span-3">
                        <label for="contractType" class="block text-sm font-medium text-gray-700">Contract Type</label>
                        <select id="contractType" name="contractType" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                            <option value="">Select Contract Type</option>
                            <option value="NCSC">NCSC</option>
                            <option value="CS">CS</option>
                            <option value="Part-Time">Part-Time</option>
                        </select>
                    </div>

                    <div class="sm:col-span-3">
                        <label for="status" class="block text-sm font-medium text-gray-700">Status</label>
                        <select id="status" name="status" required class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                            <option value="">Select Status</option>
                            <option value="active">Active</option>
                            <option value="pending">Pending</option>
                            <option value="resigned">Resigned</option>
                            <option value="disabled">Disabled</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Contact Information Section -->
            <div>
                <h2 class="text-lg font-medium text-gray-900 border-b pb-2">Contact Information</h2>
                <div class="mt-4 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                    <div class="sm:col-span-3">
                        <label for="officeTelephone" class="block text-sm font-medium text-gray-700">Office Telephone</label>
                        <input type="tel" name="officeTelephone" id="officeTelephone" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>

                    <div class="sm:col-span-3">
                        <label for="fax" class="block text-sm font-medium text-gray-700">Fax</label>
                        <input type="tel" name="fax" id="fax" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>

                    <div class="sm:col-span-3">
                        <label for="mobile" class="block text-sm font-medium text-gray-700">Other Telephone (Mobile)</label>
                        <input type="tel" name="mobile" id="mobile" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>

                    <div class="sm:col-span-6">
                        <label for="officeAddress" class="block text-sm font-medium text-gray-700">Office Address</label>
                        <textarea id="officeAddress" name="officeAddress" rows="3" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"></textarea>
                    </div>
                </div>
            </div>

            <!-- Rank Section -->
            <div>
                <h2 class="text-lg font-medium text-gray-900 border-b pb-2">Rank</h2>
                <div class="mt-4 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                    <div class="sm:col-span-3">
                        <label for="rankIdentifier" class="block text-sm font-medium text-gray-700">Rank Identifier</label>
                        <input type="text" name="rankIdentifier" id="rankIdentifier" readonly class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-50 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>

                    <div class="sm:col-span-3">
                        <label for="rankName" class="block text-sm font-medium text-gray-700">Rank Name</label>
                        <select id="rankName" name="rankName" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" onchange="autoFillRankDetails()">
                            <option value="">Select Rank Name</option>
                            <option value="Clerical Officer">Clerical Officer</option>
                            <option value="Junior User">Junior User</option>
                            <option value="System Admin">System Admin</option>
                            <option value="Section Admin">Section Admin</option>
                            <option value="Division Admin">Division Admin</option>
                            <option value="PR Officer">PR Officer</option>
                        </select>
                    </div>

                    <div class="sm:col-span-3">
                        <label for="abbrev" class="block text-sm font-medium text-gray-700">Abbrev</label>
                        <input type="text" name="abbrev" id="abbrev" readonly class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-50 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>

                    <div class="sm:col-span-3">
                        <label for="section" class="block text-sm font-medium text-gray-700">Section</label>
                        <select id="section" name="section" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" onchange="autoFillOrganizationDetails()">
                            <option value="">Select Section</option>
                            <option value="Revenue Section">Revenue Section</option>
                            <option value="Administrative Services Section 2">Administrative Services Section 2</option>
                            <option value="HR Section">HR Section</option>
                            <option value="Finance Section">Finance Section</option>
                            <option value="Legal Section">Legal Section</option>
                            <option value="Operations Section">Operations Section</option>
                        </select>
                    </div>

                    <div class="sm:col-span-3">
                        <label for="branch" class="block text-sm font-medium text-gray-700">Branch</label>
                        <input type="text" name="branch" id="branch" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>

                    <div class="sm:col-span-3">
                        <label for="division" class="block text-sm font-medium text-gray-700">Division</label>
                        <input type="text" name="division" id="division" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>

                    <div class="sm:col-span-3">
                        <label for="team" class="block text-sm font-medium text-gray-700">Team</label>
                        <input type="text" name="team" id="team" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>

                    <div class="sm:col-span-3">
                        <label for="rankSupervisor" class="block text-sm font-medium text-gray-700">Rank Supervisor</label>
                        <input type="text" name="rankSupervisor" id="rankSupervisor" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>
                </div>
            </div>

            <!-- Supervisor List Section -->
            <div>
                <h2 class="text-lg font-medium text-gray-900 border-b pb-2">Supervisor List</h2>
                <div class="mt-4 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                    <div class="sm:col-span-3">
                        <label for="appraisingOfficer" class="block text-sm font-medium text-gray-700">Appraising Officer</label>
                        <select name="appraisingOfficer" id="appraisingOfficer" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                            <option value="">Select Appraising Officer</option>
                            <!-- Options will be populated dynamically -->
                        </select>
                    </div>

                    <div class="sm:col-span-3">
                        <label for="countersigningOfficer" class="block text-sm font-medium text-gray-700">Countersigning Officer</label>
                        <select name="countersigningOfficer" id="countersigningOfficer" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                            <option value="">Select Countersigning Officer</option>
                            <!-- Options will be populated dynamically -->
                        </select>
                    </div>
                </div>
            </div>

            <!-- Rank-based Permissions Section (Only visible to PR Officers) -->
            <div id="rankPermissionsSection" class="hidden">
                <h2 class="text-lg font-medium text-gray-900 border-b pb-2">Rank-based Permissions</h2>
                <p class="mt-2 text-sm text-gray-500">Manage function-level permissions based on rank identifier.</p>

                <div class="mt-4 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                    <div class="sm:col-span-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Permission Matrix</label>
                        <div class="border border-gray-300 rounded-md p-4 max-h-60 overflow-y-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Function</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Access</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">User Management</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <input type="checkbox" name="perm_user_management" id="perm_user_management" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Document Access</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <input type="checkbox" name="perm_document_access" id="perm_document_access" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Report Generation</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <input type="checkbox" name="perm_report_generation" id="perm_report_generation" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">System Configuration</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <input type="checkbox" name="perm_system_config" id="perm_system_config" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Rank Management</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <input type="checkbox" name="perm_rank_management" id="perm_rank_management" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Personnel History Section -->
            <div>
                <h2 class="text-lg font-medium text-gray-900 border-b pb-2">Personnel History</h2>
                <div class="mt-4">
                    <!-- Add New History Entry Form -->
                    <div class="bg-gray-50 p-4 rounded-md border border-gray-200 mb-6">
                        <h3 class="text-md font-medium text-gray-900 mb-4">Initial Position</h3>
                        <div class="grid grid-cols-1 gap-y-4 gap-x-4 sm:grid-cols-6">
                            <div class="sm:col-span-2">
                                <label for="historyDate" class="block text-sm font-medium text-gray-700">Start Date</label>
                                <input type="date" id="historyDate" name="historyDate" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                            </div>

                            <div class="sm:col-span-2">
                                <label for="historyEventType" class="block text-sm font-medium text-gray-700">Event Type</label>
                                <select id="historyEventType" name="historyEventType" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                    <option value="newPosition" selected>New Position</option>
                                    <option value="promotion">Promotion</option>
                                    <option value="transfer">Transfer</option>
                                </select>
                            </div>

                            <div class="sm:col-span-2">
                                <label for="historyPosition" class="block text-sm font-medium text-gray-700">Position</label>
                                <input type="text" id="historyPosition" name="historyPosition" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                            </div>

                            <div class="sm:col-span-6">
                                <label for="historyDetails" class="block text-sm font-medium text-gray-700">Details</label>
                                <textarea id="historyDetails" name="historyDetails" rows="2" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="Initial position details"></textarea>
                            </div>
                        </div>
                    </div>

                    <p class="text-sm text-gray-500 mb-4">Additional history records can be added after the account is created.</p>
                </div>
            </div>

            <!-- Account Details Section -->
            <div>
                <h2 class="text-lg font-medium text-gray-900 border-b pb-2">Account Details</h2>
                <div class="mt-4 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                    <div class="sm:col-span-3">
                        <label for="username" class="block text-sm font-medium text-gray-700">Preferred Username</label>
                        <input type="text" name="username" id="username" required class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                        <p class="mt-1 text-xs text-gray-500">Will be used for Active Directory account</p>
                        <p id="usernameError" class="mt-1 text-xs text-red-600 hidden">The Username exists. Please use another one</p>
                    </div>



                    <div class="sm:col-span-3">
                        <label for="accessLevel" class="block text-sm font-medium text-gray-700">Access Level</label>
                        <select id="accessLevel" name="accessLevel" required class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                            <option value="">Select Access Level</option>
                            <option value="Basic">Basic</option>
                            <option value="Standard">Standard</option>
                            <option value="Advanced">Advanced</option>
                            <option value="Full">Full</option>
                        </select>
                    </div>

                    <div class="sm:col-span-6">
                        <label for="notes" class="block text-sm font-medium text-gray-700">Additional Notes</label>
                        <textarea id="notes" name="notes" rows="3" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"></textarea>
                    </div>
                </div>
            </div>

            <div class="flex justify-end space-x-3">
                <button type="button" onclick="window.location.href='index.html'" class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Cancel
                </button>
                <button type="submit" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Create Account
                </button>
            </div>
        </form>
        </div>

        <!-- Pending Account Creation Tab Content -->
        <div id="pendingAccountsContent" class="hidden">
            <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm text-yellow-700">
                            This is a read-only view of pending account creations. These accounts are awaiting Active Directory setup.
                        </p>
                    </div>
                </div>
            </div>

            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Username</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Position</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date of Join</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rank Identifier</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200" id="pendingAccountsTable">
                        <!-- Sample data - will be replaced by dynamic content -->
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">jsmith</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Junior Analyst</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-05-15</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">JA-001</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">Pending</span>
                            </td>
                        </tr>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">mwilliams</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Senior Developer</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-06-01</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">SD-002</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">Pending</span>
                            </td>
                        </tr>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">alee</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">PR Officer</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-06-15</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">PR-003</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">Pending</span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Success Modal -->
    <div id="successModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden">
        <div class="bg-white p-6 rounded-lg shadow-xl max-w-md w-full">
            <div class="text-center">
                <svg class="mx-auto h-12 w-12 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <h3 class="mt-2 text-lg font-medium text-gray-900">Account Creation Successful</h3>
                <p class="mt-1 text-sm text-gray-500">
                    The user account has been created successfully. An IT Section Officer has been notified to create the Active Directory account.
                </p>
                <div class="mt-4">
                    <button type="button" onclick="closeModal()" class="inline-flex justify-center px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700 focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-indigo-500">
                        Return to User List
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Function to toggle visibility of rank permissions section based on rank name
        function toggleRankPermissionsVisibility() {
            const rankNameInput = document.getElementById('rankName');
            const rankPermissionsSection = document.getElementById('rankPermissionsSection');

            // Only show rank permissions section for PR Officers
            if (rankNameInput.value.includes('PR Officer')) {
                rankPermissionsSection.classList.remove('hidden');
            } else {
                rankPermissionsSection.classList.add('hidden');
            }
        }

        // Function to auto-fill organization details when section is selected
        function autoFillOrganizationDetails() {
            const sectionValue = document.getElementById('section').value;

            // Define organization structure mapping
            const organizationMapping = {
                'Revenue Section': {
                    branch: 'Support Branch',
                    division: 'Finance Division',
                    team: '-'
                },
                'IT Section': {
                    branch: 'Technology Branch',
                    division: 'IT Division',
                    team: 'IT Support Team'
                },
                'HR Section': {
                    branch: 'Administration Branch',
                    division: 'Human Resources Division',
                    team: 'HR Team'
                },
                'Finance Section': {
                    branch: 'Finance Branch',
                    division: 'Finance Division',
                    team: 'Finance Team'
                },
                'Legal Section': {
                    branch: 'Legal Branch',
                    division: 'Legal Division',
                    team: 'Legal Team'
                },
                'Operations Section': {
                    branch: 'Operations Branch',
                    division: 'Operations Division',
                    team: 'Operations Team'
                }
            };

            if (sectionValue && organizationMapping[sectionValue]) {
                const mapping = organizationMapping[sectionValue];
                document.getElementById('branch').value = mapping.branch;
                document.getElementById('division').value = mapping.division;
                document.getElementById('team').value = mapping.team;
            }
        }

        // Function to switch between tabs
        function showTab(tabName) {
            // Hide all tab contents
            document.getElementById('createAccountContent').classList.add('hidden');
            document.getElementById('pendingAccountsContent').classList.add('hidden');

            // Remove active class from all tabs
            document.getElementById('createAccountTab').classList.remove('border-indigo-500', 'text-indigo-600');
            document.getElementById('createAccountTab').classList.add('border-transparent', 'text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300');
            document.getElementById('pendingAccountsTab').classList.remove('border-indigo-500', 'text-indigo-600');
            document.getElementById('pendingAccountsTab').classList.add('border-transparent', 'text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300');

            // Show selected tab content and mark tab as active
            if (tabName === 'createAccount') {
                document.getElementById('createAccountContent').classList.remove('hidden');
                document.getElementById('createAccountTab').classList.remove('border-transparent', 'text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300');
                document.getElementById('createAccountTab').classList.add('border-indigo-500', 'text-indigo-600');
            } else if (tabName === 'pendingAccounts') {
                document.getElementById('pendingAccountsContent').classList.remove('hidden');
                document.getElementById('pendingAccountsTab').classList.remove('border-transparent', 'text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300');
                document.getElementById('pendingAccountsTab').classList.add('border-indigo-500', 'text-indigo-600');
                loadPendingAccounts();
            }
        }

        // Function to load pending accounts
        function loadPendingAccounts() {
            // Get users from localStorage
            const users = JSON.parse(localStorage.getItem('users')) || [];

            // Filter for pending users
            const pendingUsers = users.filter(user => user.status === 'pending');

            // Get the table body
            const tableBody = document.getElementById('pendingAccountsTable');

            // Clear existing rows
            tableBody.innerHTML = '';

            // Add rows for each pending user
            if (pendingUsers.length === 0) {
                // If no pending users, show a message
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td colspan="5" class="px-6 py-4 text-center text-sm text-gray-500">
                        No pending accounts found.
                    </td>
                `;
                tableBody.appendChild(row);
            } else {
                pendingUsers.forEach(user => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${user.username || ''}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${user.position || ''}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${user.dateOfJoin || ''}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${user.rankIdentifier || ''}</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">Pending</span>
                        </td>
                    `;
                    tableBody.appendChild(row);
                });
            }
        }

        // Initialize permissions visibility on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Show the create account tab by default
            showTab('createAccount');

            toggleRankPermissionsVisibility();

            // Load existing ranks from localStorage if available
            loadExistingRanks();

            // Populate supervisor dropdowns
            populateSupervisorDropdowns();

            // Add event listener to rank name input
            document.getElementById('rankName').addEventListener('input', toggleRankPermissionsVisibility);
        });

        // Function to populate supervisor dropdowns
        function populateSupervisorDropdowns() {
            // Get existing users and supervisor lists from localStorage
            const existingUsers = JSON.parse(localStorage.getItem('users')) || [];
            const supervisorLists = JSON.parse(localStorage.getItem('supervisorLists')) || {
                appraisingOfficers: [],
                countersigningOfficers: []
            };

            // Get the dropdown elements
            const appraisingOfficerDropdown = document.getElementById('appraisingOfficer');
            const countersigningOfficerDropdown = document.getElementById('countersigningOfficer');

            // Store the currently selected values (if any)
            const selectedAppraising = appraisingOfficerDropdown.value;
            const selectedCountersigning = countersigningOfficerDropdown.value;

            // Clear existing options except the first one
            while (appraisingOfficerDropdown.options.length > 1) {
                appraisingOfficerDropdown.remove(1);
            }

            while (countersigningOfficerDropdown.options.length > 1) {
                countersigningOfficerDropdown.remove(1);
            }

            // Add options for appraising officers from the managed list
            supervisorLists.appraisingOfficers.forEach(username => {
                const user = existingUsers.find(u => u.username === username);
                if (user && user.status === 'active') {
                    const option = document.createElement('option');
                    option.value = user.username;
                    option.textContent = `${user.firstName} ${user.lastName} (${user.rankName || 'No Rank'})`;
                    // Set selected if this was the previously selected option
                    if (user.username === selectedAppraising) {
                        option.selected = true;
                    }
                    appraisingOfficerDropdown.appendChild(option);
                }
            });

            // Add options for countersigning officers from the managed list
            supervisorLists.countersigningOfficers.forEach(username => {
                const user = existingUsers.find(u => u.username === username);
                if (user && user.status === 'active') {
                    const option = document.createElement('option');
                    option.value = user.username;
                    option.textContent = `${user.firstName} ${user.lastName} (${user.rankName || 'No Rank'})`;
                    // Set selected if this was the previously selected option
                    if (user.username === selectedCountersigning) {
                        option.selected = true;
                    }
                    countersigningOfficerDropdown.appendChild(option);
                }
            });
        }

        // Function to load existing ranks from localStorage
        function loadExistingRanks() {
            const ranks = JSON.parse(localStorage.getItem('ranks')) || [];
            console.log('Loaded ranks:', ranks);

            // Populate the rank identifier dropdown
            const rankIdentifierDropdown = document.getElementById('rankIdentifier');

            // Clear existing options except the first one
            while (rankIdentifierDropdown.options.length > 1) {
                rankIdentifierDropdown.remove(1);
            }

            // Add options for each rank
            ranks.forEach(rank => {
                const option = document.createElement('option');
                // Use rankIdentifier property which is used in permissions.html
                option.value = rank.rankIdentifier;
                option.textContent = rank.rankIdentifier;
                rankIdentifierDropdown.appendChild(option);
            });
        }

        // Function to auto-fill rank details when rank name is selected
        function autoFillRankDetails() {
            const rankName = document.getElementById('rankName').value;
            if (!rankName) {
                // Clear fields if no rank name selected
                document.getElementById('rankIdentifier').value = '';
                document.getElementById('abbrev').value = '';
                toggleRankPermissionsVisibility();
                return;
            }

            // Define rank mapping with identifiers and abbreviations
            const rankMapping = {
                'Clerical Officer': {
                    identifier: 'CO',
                    abbrev: 'CO'
                },
                'Junior User': {
                    identifier: 'JU-001',
                    abbrev: 'JU'
                },
                'System Admin': {
                    identifier: 'SA-001',
                    abbrev: 'SA'
                },
                'Section Admin': {
                    identifier: 'SEC-001',
                    abbrev: 'SEC'
                },
                'Division Admin': {
                    identifier: 'DIV-001',
                    abbrev: 'DIV'
                },
                'PR Officer': {
                    identifier: 'PR-001',
                    abbrev: 'PR'
                }
            };

            // Auto-fill rank identifier and abbreviation
            if (rankMapping[rankName]) {
                document.getElementById('rankIdentifier').value = rankMapping[rankName].identifier;
                document.getElementById('abbrev').value = rankMapping[rankName].abbrev;
            }

            // Trigger the rank permissions visibility toggle
            toggleRankPermissionsVisibility();

            // Get ranks from localStorage to check for existing permissions
            const ranks = JSON.parse(localStorage.getItem('ranks')) || [];
            const selectedRank = ranks.find(rank => rank.rankName === rankName);

            // If it's a PR Officer rank and has saved permissions, set the permissions checkboxes
            if (rankName.includes('PR Officer') && selectedRank && selectedRank.permissions) {
                document.getElementById('perm_user_management').checked = selectedRank.permissions.userManagement || false;
                document.getElementById('perm_document_access').checked = selectedRank.permissions.documentAccess || false;
                document.getElementById('perm_report_generation').checked = selectedRank.permissions.reportGeneration || false;
                document.getElementById('perm_system_config').checked = selectedRank.permissions.systemConfig || false;
                document.getElementById('perm_rank_management').checked = selectedRank.permissions.rankManagement || false;
            }
        }

        // Function to save rank permissions to localStorage
        function saveRankPermissions() {
            const rankIdentifier = document.getElementById('rankIdentifier').value;
            if (!rankIdentifier) return;

            const permissions = {
                userManagement: document.getElementById('perm_user_management').checked,
                documentAccess: document.getElementById('perm_document_access').checked,
                reportGeneration: document.getElementById('perm_report_generation').checked,
                systemConfig: document.getElementById('perm_system_config').checked,
                rankManagement: document.getElementById('perm_rank_management').checked,
                // Add standard permissions used in permissions.html
                createUser: document.getElementById('perm_user_management').checked,
                viewUser: true, // Default to true
                editUser: document.getElementById('perm_user_management').checked,
                deleteUser: document.getElementById('perm_user_management').checked
            };

            // Get existing ranks or initialize empty array
            const ranks = JSON.parse(localStorage.getItem('ranks')) || [];

            // Check if rank already exists
            const existingRankIndex = ranks.findIndex(rank => rank.rankIdentifier === rankIdentifier);

            if (existingRankIndex >= 0) {
                // Update existing rank
                ranks[existingRankIndex].permissions = permissions;
            } else {
                // Add new rank with permissions
                ranks.push({
                    rankIdentifier: rankIdentifier,
                    rankName: document.getElementById('rankName').value,
                    branch: document.getElementById('branch').value,
                    division: document.getElementById('division').value,
                    section: document.getElementById('section').value,
                    team: document.getElementById('team').value,
                    permissions: permissions
                });
            }

            // Save updated ranks to localStorage
            localStorage.setItem('ranks', JSON.stringify(ranks));
            console.log('Saved rank permissions for:', rankIdentifier);
        }

        // Real-time username validation
        document.getElementById('username').addEventListener('input', function() {
            const username = this.value.trim();
            const errorElement = document.getElementById('usernameError');

            if (username.length > 0) {
                // Get existing users from localStorage
                const existingUsers = JSON.parse(localStorage.getItem('users')) || [];

                // Check if username already exists
                const usernameExists = existingUsers.some(user => user.username === username);

                if (usernameExists) {
                    // Show error message
                    errorElement.classList.remove('hidden');
                    this.classList.add('border-red-500');
                    this.classList.remove('border-gray-300');
                } else {
                    // Hide error message
                    errorElement.classList.add('hidden');
                    this.classList.remove('border-red-500');
                    this.classList.add('border-gray-300');
                }
            } else {
                // Hide error message when field is empty
                errorElement.classList.add('hidden');
                this.classList.remove('border-red-500');
                this.classList.add('border-gray-300');
            }
        });

        // Form submission handler
        document.getElementById('createAccountForm').addEventListener('submit', function(e) {
            e.preventDefault();

            // Get form data
            const formData = new FormData(this);
            const userData = {};

            // Convert FormData to object
            for (let [key, value] of formData.entries()) {
                userData[key] = value;
            }

            // Get existing users from localStorage or initialize empty array
            const existingUsers = JSON.parse(localStorage.getItem('users')) || [];

            // Check if username already exists
            const usernameExists = existingUsers.some(user => user.username === userData.username);

            if (usernameExists) {
                // Show error message
                alert("The Username exists. Please use another one");
                // Focus on the username field
                document.getElementById('username').focus();
                return; // Stop form submission
            }

            // If user has a rank name that includes PR Officer and has modified rank permissions, save those too
            if (userData.rankName && userData.rankName.includes('PR Officer') && userData.rankIdentifier) {
                saveRankPermissions();
            }

            // Use the selected status or default to pending if not selected
            if (!userData.status) {
                userData.status = 'pending';
            }

            // Add contact information fields
            userData.officeTelephone = document.getElementById('officeTelephone').value;
            userData.fax = document.getElementById('fax').value;
            userData.mobile = document.getElementById('mobile').value;
            userData.officeAddress = document.getElementById('officeAddress').value;

            // Add abbrev field
            userData.abbrev = document.getElementById('abbrev').value;

            // Initialize personnel history array with initial position if provided
            userData.personnelHistory = [];

            // Add initial position to personnel history if data is provided
            const historyDate = document.getElementById('historyDate').value;
            const historyEventType = document.getElementById('historyEventType').value;
            const historyPosition = document.getElementById('historyPosition').value;
            const historyDetails = document.getElementById('historyDetails').value;

            if (historyDate && historyPosition) {
                userData.personnelHistory.push({
                    date: historyDate,
                    eventType: historyEventType,
                    position: historyPosition,
                    details: historyDetails || 'Initial position'
                });
            }

            userData.id = Date.now().toString(); // Simple unique ID
            existingUsers.push(userData);

            // Save to localStorage
            localStorage.setItem('users', JSON.stringify(existingUsers));

            // Show success modal
            document.getElementById('successModal').classList.remove('hidden');

            // Refresh the pending accounts tab
            loadPendingAccounts();

            // Simulate notification to IT Section Officer
            console.log('Notification sent to IT Section Officer for user:', userData.username);
        });

        // Close modal and show pending accounts tab
        function closeModal() {
            document.getElementById('successModal').classList.add('hidden');
            // Show the pending accounts tab
            showTab('pendingAccounts');
        }
    </script>
</body>
</html>

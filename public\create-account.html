<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create User Account - OFCA Portal</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="css/ofca-styles.css">
    <script src="js/header.js"></script>
    <script src="js/footer.js"></script>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="p-8">
    <div class="bg-white p-8 rounded-lg shadow-lg w-full max-w-4xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold">Create User Account</h1>
            <a href="index.html" class="text-indigo-600 hover:text-indigo-900">Back to User List</a>
        </div>

        <form id="createAccountForm" class="space-y-6">
            <!-- Personal Information Section -->
            <div>
                <h2 class="text-lg font-medium text-gray-900 border-b pb-2">Personal Information</h2>
                <div class="mt-4 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                    <div class="sm:col-span-3">
                        <label for="firstName" class="block text-sm font-medium text-gray-700">First Name</label>
                        <input type="text" name="firstName" id="firstName" required class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>

                    <div class="sm:col-span-3">
                        <label for="lastName" class="block text-sm font-medium text-gray-700">Last Name</label>
                        <input type="text" name="lastName" id="lastName" required class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>

                    <div class="sm:col-span-3">
                        <label for="email" class="block text-sm font-medium text-gray-700">Email Address</label>
                        <input type="email" name="email" id="email" required class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>

                    <div class="sm:col-span-3">
                        <label for="phone" class="block text-sm font-medium text-gray-700">Phone Number</label>
                        <input type="tel" name="phone" id="phone" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>
                </div>
            </div>

            <!-- Start Date Section -->
            <div>
                <h2 class="text-lg font-medium text-gray-900 border-b pb-2">Employment Information</h2>
                <div class="mt-4 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                    <div class="sm:col-span-3">
                        <label for="startDate" class="block text-sm font-medium text-gray-700">Start Date</label>
                        <input type="date" name="startDate" id="startDate" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>
                </div>
            </div>

            <!-- Rank Section -->
            <div>
                <h2 class="text-lg font-medium text-gray-900 border-b pb-2">Rank</h2>
                <div class="mt-4 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                    <div class="sm:col-span-3">
                        <label for="rankIdentifier" class="block text-sm font-medium text-gray-700">Rank Identifier</label>
                        <input type="text" name="rankIdentifier" id="rankIdentifier" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>

                    <div class="sm:col-span-3">
                        <label for="rankName" class="block text-sm font-medium text-gray-700">Rank Name</label>
                        <select id="rankName" name="rankName" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" onchange="toggleRankPermissionsVisibility()">
                            <option value="">Select Rank Name</option>
                            <option value="Senior User">Senior User</option>
                            <option value="Junior User">Junior User</option>
                            <option value="System Admin">System Admin</option>
                            <option value="Section Admin">Section Admin</option>
                            <option value="Division Admin">Division Admin</option>
                            <option value="PR Officer">PR Officer</option>
                        </select>
                    </div>

                    <div class="sm:col-span-3">
                        <label for="branch" class="block text-sm font-medium text-gray-700">Branch</label>
                        <input type="text" name="branch" id="branch" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>

                    <div class="sm:col-span-3">
                        <label for="division" class="block text-sm font-medium text-gray-700">Division</label>
                        <input type="text" name="division" id="division" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>

                    <div class="sm:col-span-3">
                        <label for="section" class="block text-sm font-medium text-gray-700">Section</label>
                        <input type="text" name="section" id="section" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>

                    <div class="sm:col-span-3">
                        <label for="team" class="block text-sm font-medium text-gray-700">Team</label>
                        <input type="text" name="team" id="team" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>
                </div>
            </div>

            <!-- Rank-based Permissions Section (Only visible to PR Officers) -->
            <div id="rankPermissionsSection" class="hidden">
                <h2 class="text-lg font-medium text-gray-900 border-b pb-2">Rank-based Permissions</h2>
                <p class="mt-2 text-sm text-gray-500">Manage function-level permissions based on rank identifier.</p>

                <div class="mt-4 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                    <div class="sm:col-span-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Permission Matrix</label>
                        <div class="border border-gray-300 rounded-md p-4 max-h-60 overflow-y-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Function</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Access</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">User Management</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <input type="checkbox" name="perm_user_management" id="perm_user_management" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Document Access</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <input type="checkbox" name="perm_document_access" id="perm_document_access" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Report Generation</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <input type="checkbox" name="perm_report_generation" id="perm_report_generation" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">System Configuration</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <input type="checkbox" name="perm_system_config" id="perm_system_config" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Rank Management</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <input type="checkbox" name="perm_rank_management" id="perm_rank_management" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Account Details Section -->
            <div>
                <h2 class="text-lg font-medium text-gray-900 border-b pb-2">Account Details</h2>
                <div class="mt-4 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                    <div class="sm:col-span-3">
                        <label for="username" class="block text-sm font-medium text-gray-700">Preferred Username</label>
                        <input type="text" name="username" id="username" required class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                        <p class="mt-1 text-xs text-gray-500">Will be used for Active Directory account</p>
                        <p id="usernameError" class="mt-1 text-xs text-red-600 hidden">The Username exists. Please use another one</p>
                    </div>

                    <div class="sm:col-span-3">
                        <label for="status" class="block text-sm font-medium text-gray-700">Status</label>
                        <select id="status" name="status" required class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                            <option value="">Select Status</option>
                            <option value="active">Active</option>
                            <option value="pending">Pending</option>
                            <option value="resigned">Resigned</option>
                            <option value="disabled">Disabled</option>
                        </select>
                    </div>

                    <div class="sm:col-span-3">
                        <label for="accessLevel" class="block text-sm font-medium text-gray-700">Access Level</label>
                        <select id="accessLevel" name="accessLevel" required class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                            <option value="">Select Access Level</option>
                            <option value="Basic">Basic</option>
                            <option value="Standard">Standard</option>
                            <option value="Advanced">Advanced</option>
                            <option value="Full">Full</option>
                        </select>
                    </div>

                    <div class="sm:col-span-6">
                        <label for="notes" class="block text-sm font-medium text-gray-700">Additional Notes</label>
                        <textarea id="notes" name="notes" rows="3" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"></textarea>
                    </div>
                </div>
            </div>

            <div class="flex justify-end space-x-3">
                <button type="button" onclick="window.location.href='index.html'" class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Cancel
                </button>
                <button type="submit" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Create Account
                </button>
            </div>
        </form>
    </div>

    <!-- Success Modal -->
    <div id="successModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden">
        <div class="bg-white p-6 rounded-lg shadow-xl max-w-md w-full">
            <div class="text-center">
                <svg class="mx-auto h-12 w-12 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <h3 class="mt-2 text-lg font-medium text-gray-900">Account Creation Successful</h3>
                <p class="mt-1 text-sm text-gray-500">
                    The user account has been created successfully. An IT Section Officer has been notified to create the Active Directory account.
                </p>
                <div class="mt-4">
                    <button type="button" onclick="closeModal()" class="inline-flex justify-center px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700 focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-indigo-500">
                        Return to User List
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Function to toggle visibility of rank permissions section based on rank name
        function toggleRankPermissionsVisibility() {
            const rankNameInput = document.getElementById('rankName');
            const rankPermissionsSection = document.getElementById('rankPermissionsSection');

            // Only show rank permissions section for PR Officers
            if (rankNameInput.value.includes('PR Officer')) {
                rankPermissionsSection.classList.remove('hidden');
            } else {
                rankPermissionsSection.classList.add('hidden');
            }
        }

        // Initialize permissions visibility on page load
        document.addEventListener('DOMContentLoaded', function() {
            toggleRankPermissionsVisibility();

            // Load existing ranks from localStorage if available
            loadExistingRanks();

            // Add event listener to rank name input
            document.getElementById('rankName').addEventListener('input', toggleRankPermissionsVisibility);
        });

        // Function to load existing ranks from localStorage
        function loadExistingRanks() {
            const ranks = JSON.parse(localStorage.getItem('ranks')) || [];
            console.log('Loaded ranks:', ranks);

            // Here you could populate a dropdown with existing ranks if needed
        }

        // Function to save rank permissions to localStorage
        function saveRankPermissions() {
            const rankIdentifier = document.getElementById('rankIdentifier').value;
            if (!rankIdentifier) return;

            const permissions = {
                userManagement: document.getElementById('perm_user_management').checked,
                documentAccess: document.getElementById('perm_document_access').checked,
                reportGeneration: document.getElementById('perm_report_generation').checked,
                systemConfig: document.getElementById('perm_system_config').checked,
                rankManagement: document.getElementById('perm_rank_management').checked
            };

            // Get existing ranks or initialize empty array
            const ranks = JSON.parse(localStorage.getItem('ranks')) || [];

            // Check if rank already exists
            const existingRankIndex = ranks.findIndex(rank => rank.identifier === rankIdentifier);

            if (existingRankIndex >= 0) {
                // Update existing rank
                ranks[existingRankIndex].permissions = permissions;
            } else {
                // Add new rank with permissions
                ranks.push({
                    identifier: rankIdentifier,
                    name: document.getElementById('rankName').value,
                    branch: document.getElementById('branch').value,
                    division: document.getElementById('division').value,
                    section: document.getElementById('section').value,
                    team: document.getElementById('team').value,
                    permissions: permissions
                });
            }

            // Save updated ranks to localStorage
            localStorage.setItem('ranks', JSON.stringify(ranks));
            console.log('Saved rank permissions for:', rankIdentifier);
        }

        // Real-time username validation
        document.getElementById('username').addEventListener('input', function() {
            const username = this.value.trim();
            const errorElement = document.getElementById('usernameError');

            if (username.length > 0) {
                // Get existing users from localStorage
                const existingUsers = JSON.parse(localStorage.getItem('users')) || [];

                // Check if username already exists
                const usernameExists = existingUsers.some(user => user.username === username);

                if (usernameExists) {
                    // Show error message
                    errorElement.classList.remove('hidden');
                    this.classList.add('border-red-500');
                    this.classList.remove('border-gray-300');
                } else {
                    // Hide error message
                    errorElement.classList.add('hidden');
                    this.classList.remove('border-red-500');
                    this.classList.add('border-gray-300');
                }
            } else {
                // Hide error message when field is empty
                errorElement.classList.add('hidden');
                this.classList.remove('border-red-500');
                this.classList.add('border-gray-300');
            }
        });

        // Form submission handler
        document.getElementById('createAccountForm').addEventListener('submit', function(e) {
            e.preventDefault();

            // Get form data
            const formData = new FormData(this);
            const userData = {};

            // Convert FormData to object
            for (let [key, value] of formData.entries()) {
                userData[key] = value;
            }

            // Get existing users from localStorage or initialize empty array
            const existingUsers = JSON.parse(localStorage.getItem('users')) || [];

            // Check if username already exists
            const usernameExists = existingUsers.some(user => user.username === userData.username);

            if (usernameExists) {
                // Show error message
                alert("The Username exists. Please use another one");
                // Focus on the username field
                document.getElementById('username').focus();
                return; // Stop form submission
            }

            // If user has a rank name that includes PR Officer and has modified rank permissions, save those too
            if (userData.rankName && userData.rankName.includes('PR Officer') && userData.rankIdentifier) {
                saveRankPermissions();
            }

            // Use the selected status or default to pending if not selected
            if (!userData.status) {
                userData.status = 'pending';
            }
            userData.id = Date.now().toString(); // Simple unique ID
            existingUsers.push(userData);

            // Save to localStorage
            localStorage.setItem('users', JSON.stringify(existingUsers));

            // Show success modal
            document.getElementById('successModal').classList.remove('hidden');

            // Simulate notification to IT Section Officer
            console.log('Notification sent to IT Section Officer for user:', userData.username);
        });

        // Close modal and redirect to user list
        function closeModal() {
            document.getElementById('successModal').classList.add('hidden');
            window.location.href = 'index.html';
        }
    </script>
</body>
</html>

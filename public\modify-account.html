<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modify User Account - OFCA Portal</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="css/ofca-styles.css">
    <script src="js/header.js"></script>
    <script src="js/footer.js"></script>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="p-8">
    <div class="bg-white p-8 rounded-lg shadow-lg w-full max-w-4xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold">Modify User Account</h1>
            <a href="index.html" class="text-indigo-600 hover:text-indigo-900">Back to User List</a>
        </div>

        <form id="modifyAccountForm" class="space-y-6">
            <!-- Hidden field for user ID -->
            <input type="hidden" id="userId" name="userId">

            <!-- Personal Information Section -->
            <div>
                <h2 class="text-lg font-medium text-gray-900 border-b pb-2">Personal Information</h2>
                <div class="mt-4 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                    <div class="sm:col-span-3">
                        <label for="firstName" class="block text-sm font-medium text-gray-700">First Name</label>
                        <input type="text" name="firstName" id="firstName" required class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>

                    <div class="sm:col-span-3">
                        <label for="lastName" class="block text-sm font-medium text-gray-700">Last Name</label>
                        <input type="text" name="lastName" id="lastName" required class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>

                    <div class="sm:col-span-3">
                        <label for="email" class="block text-sm font-medium text-gray-700">Email Address</label>
                        <input type="email" name="email" id="email" required class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>

                    <div class="sm:col-span-3">
                        <label for="phone" class="block text-sm font-medium text-gray-700">Phone Number</label>
                        <input type="tel" name="phone" id="phone" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>
                </div>
            </div>

            <!-- Employment Information Section -->
            <div>
                <h2 class="text-lg font-medium text-gray-900 border-b pb-2">Employment Information</h2>
                <div class="mt-4 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                    <div class="sm:col-span-3">
                        <label for="employeeId" class="block text-sm font-medium text-gray-700">Employee ID</label>
                        <input type="text" name="employeeId" id="employeeId" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>

                    <div class="sm:col-span-3">
                        <label for="dateOfJoin" class="block text-sm font-medium text-gray-700">Date of Join</label>
                        <input type="date" name="dateOfJoin" id="dateOfJoin" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>

                    <div class="sm:col-span-3">
                        <label for="position" class="block text-sm font-medium text-gray-700">Position</label>
                        <input type="text" name="position" id="position" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>

                    <div class="sm:col-span-3">
                        <label for="contractType" class="block text-sm font-medium text-gray-700">Contract Type</label>
                        <select id="contractType" name="contractType" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                            <option value="">Select Contract Type</option>
                            <option value="NCSC">NCSC</option>
                            <option value="CS">CS</option>
                            <option value="Part-Time">Part-Time</option>
                        </select>
                    </div>

                    <div class="sm:col-span-3">
                        <label for="status" class="block text-sm font-medium text-gray-700">Status</label>
                        <select id="status" name="status" required class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                            <option value="">Select Status</option>
                            <option value="active">Active</option>
                            <option value="pending">Pending</option>
                            <option value="resigned">Resigned</option>
                            <option value="disabled">Disabled</option>
                            <option value="locked">Locked</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Rank Section -->
            <div>
                <h2 class="text-lg font-medium text-gray-900 border-b pb-2">Rank</h2>
                <div class="mt-4 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                    <div class="sm:col-span-3">
                        <label for="rankIdentifier" class="block text-sm font-medium text-gray-700">Rank Identifier</label>
                        <select name="rankIdentifier" id="rankIdentifier" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" onchange="autoFillRankDetails()">
                            <option value="">Select Rank Identifier</option>
                            <!-- Options will be populated dynamically -->
                        </select>
                    </div>

                    <div class="sm:col-span-3">
                        <label for="rankName" class="block text-sm font-medium text-gray-700">Rank Name</label>
                        <select id="rankName" name="rankName" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" onchange="toggleRankPermissionsVisibility()">
                            <option value="">Select Rank Name</option>
                            <option value="Senior User">Senior User</option>
                            <option value="Junior User">Junior User</option>
                            <option value="System Admin">System Admin</option>
                            <option value="Section Admin">Section Admin</option>
                            <option value="Division Admin">Division Admin</option>
                            <option value="PR Officer">PR Officer</option>
                        </select>
                    </div>

                    <div class="sm:col-span-3">
                        <label for="branch" class="block text-sm font-medium text-gray-700">Branch</label>
                        <input type="text" name="branch" id="branch" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>

                    <div class="sm:col-span-3">
                        <label for="division" class="block text-sm font-medium text-gray-700">Division</label>
                        <input type="text" name="division" id="division" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>

                    <div class="sm:col-span-3">
                        <label for="section" class="block text-sm font-medium text-gray-700">Section</label>
                        <input type="text" name="section" id="section" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>

                    <div class="sm:col-span-3">
                        <label for="team" class="block text-sm font-medium text-gray-700">Team</label>
                        <input type="text" name="team" id="team" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>

                    <div class="sm:col-span-3">
                        <label for="rankSupervisor" class="block text-sm font-medium text-gray-700">Rank Supervisor</label>
                        <input type="text" name="rankSupervisor" id="rankSupervisor" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>
                </div>
            </div>

            <!-- Supervisor List Section -->
            <div>
                <h2 class="text-lg font-medium text-gray-900 border-b pb-2">Supervisor List</h2>
                <div class="mt-4 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                    <div class="sm:col-span-3">
                        <label for="appraisingOfficer" class="block text-sm font-medium text-gray-700">Appraising Officer</label>
                        <select name="appraisingOfficer" id="appraisingOfficer" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                            <option value="">Select Appraising Officer</option>
                            <!-- Options will be populated dynamically -->
                        </select>
                    </div>

                    <div class="sm:col-span-3">
                        <label for="countersigningOfficer" class="block text-sm font-medium text-gray-700">Countersigning Officer</label>
                        <select name="countersigningOfficer" id="countersigningOfficer" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                            <option value="">Select Countersigning Officer</option>
                            <!-- Options will be populated dynamically -->
                        </select>
                    </div>
                </div>
            </div>

            <!-- Personnel History Section -->
            <div>
                <h2 class="text-lg font-medium text-gray-900 border-b pb-2">Personnel History</h2>
                <div class="mt-4">
                    <!-- Existing History Table -->
                    <div class="overflow-x-auto mb-6">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Event Type</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Position</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Details</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody id="personnelHistoryTable" class="bg-white divide-y divide-gray-200">
                                <!-- History entries will be populated by JavaScript -->
                                <tr>
                                    <td colspan="5" class="px-6 py-4 text-center text-sm text-gray-500">No history records found.</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- Add New History Entry Form -->
                    <div class="bg-gray-50 p-4 rounded-md border border-gray-200 mb-6">
                        <h3 class="text-md font-medium text-gray-900 mb-4">Add New Personnel History</h3>
                        <div class="grid grid-cols-1 gap-y-4 gap-x-4 sm:grid-cols-6">
                            <div class="sm:col-span-2">
                                <label for="historyDate" class="block text-sm font-medium text-gray-700">Date</label>
                                <input type="date" id="historyDate" name="historyDate" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                            </div>

                            <div class="sm:col-span-2">
                                <label for="historyEventType" class="block text-sm font-medium text-gray-700">Event Type</label>
                                <select id="historyEventType" name="historyEventType" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                    <option value="">Select Event Type</option>
                                    <option value="newPosition">New Position</option>
                                    <option value="promotion">Promotion</option>
                                    <option value="transfer">Transfer</option>
                                </select>
                            </div>

                            <div class="sm:col-span-2">
                                <label for="historyPosition" class="block text-sm font-medium text-gray-700">Position</label>
                                <input type="text" id="historyPosition" name="historyPosition" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                            </div>

                            <div class="sm:col-span-6">
                                <label for="historyDetails" class="block text-sm font-medium text-gray-700">Details</label>
                                <textarea id="historyDetails" name="historyDetails" rows="2" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"></textarea>
                            </div>

                            <div class="sm:col-span-6 flex justify-end">
                                <button type="button" id="addHistoryEntry" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                    Save
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Account Details Section -->
            <div>
                <h2 class="text-lg font-medium text-gray-900 border-b pb-2">Account Details</h2>
                <div class="mt-4 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                    <div class="sm:col-span-3">
                        <label for="username" class="block text-sm font-medium text-gray-700">Username</label>
                        <input type="text" name="username" id="username" required readonly class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-100 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                        <p class="mt-1 text-xs text-gray-500">Username cannot be changed</p>
                    </div>



                    <div class="sm:col-span-3">
                        <label for="accessLevel" class="block text-sm font-medium text-gray-700">Access Level</label>
                        <select id="accessLevel" name="accessLevel" required class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                            <option value="">Select Access Level</option>
                            <option value="Basic">Basic</option>
                            <option value="Standard">Standard</option>
                            <option value="Advanced">Advanced</option>
                            <option value="Full">Full</option>
                        </select>
                    </div>

                    <div class="sm:col-span-6">
                        <label for="notes" class="block text-sm font-medium text-gray-700">Additional Notes</label>
                        <textarea id="notes" name="notes" rows="3" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"></textarea>
                    </div>
                </div>
            </div>

            <div class="flex justify-end space-x-3">
                <button type="button" onclick="window.location.href='index.html'" class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Cancel
                </button>
                <button type="submit" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Save Changes
                </button>
            </div>
        </form>
    </div>

    <!-- Success Modal -->
    <div id="successModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden">
        <div class="bg-white p-6 rounded-lg shadow-xl max-w-md w-full">
            <div class="text-center">
                <svg class="mx-auto h-12 w-12 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <h3 class="mt-2 text-lg font-medium text-gray-900">Account Updated Successfully</h3>
                <p class="mt-1 text-sm text-gray-500">
                    The user account has been updated successfully.
                </p>
                <div class="mt-4">
                    <button type="button" onclick="closeModal()" class="inline-flex justify-center px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700 focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-indigo-500">
                        Return to User List
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Function to toggle visibility of rank permissions section based on rank name
        function toggleRankPermissionsVisibility() {
            const rankNameInput = document.getElementById('rankName');
            // If we add a permissions section later, we can toggle it here based on rank name
        }

        // Function to load existing ranks from localStorage
        function loadExistingRanks() {
            const ranks = JSON.parse(localStorage.getItem('ranks')) || [];
            console.log('Loaded ranks:', ranks);

            // Populate the rank identifier dropdown
            const rankIdentifierDropdown = document.getElementById('rankIdentifier');

            // Clear existing options except the first one
            while (rankIdentifierDropdown.options.length > 1) {
                rankIdentifierDropdown.remove(1);
            }

            // Add options for each rank
            ranks.forEach(rank => {
                const option = document.createElement('option');
                // Use rankIdentifier property which is used in permissions.html
                option.value = rank.rankIdentifier;
                option.textContent = rank.rankIdentifier;
                rankIdentifierDropdown.appendChild(option);
            });

            // Set the selected value based on the user's current rank identifier
            if (user.rankIdentifier) {
                rankIdentifierDropdown.value = user.rankIdentifier;
            }
        }

        // Function to auto-fill rank details when rank identifier is selected
        function autoFillRankDetails() {
            const rankIdentifier = document.getElementById('rankIdentifier').value;
            if (!rankIdentifier) return;

            // Get ranks from localStorage
            const ranks = JSON.parse(localStorage.getItem('ranks')) || [];

            // Find the selected rank using rankIdentifier property
            const selectedRank = ranks.find(rank => rank.rankIdentifier === rankIdentifier);

            if (selectedRank) {
                // Auto-fill the rank details
                document.getElementById('rankName').value = selectedRank.rankName || '';
                document.getElementById('branch').value = selectedRank.branch || '';
                document.getElementById('division').value = selectedRank.division || '';
                document.getElementById('section').value = selectedRank.section || '';
                document.getElementById('team').value = selectedRank.team || '';

                // Trigger the rank permissions visibility toggle
                toggleRankPermissionsVisibility();
            }
        }

        // Get user data from URL parameter
        const urlParams = new URLSearchParams(window.location.search);
        const username = urlParams.get('user');

        // Get users from localStorage
        const users = JSON.parse(localStorage.getItem('users')) || [];

        // Find the user to modify
        const user = users.find(u => u.username === username);

        // If user not found, redirect to index
        if (!user) {
            alert('User not found');
            window.location.href = 'index.html';
        }

        // Store the user ID in a global variable for later use
        const currentUserId = user.id;

        // Populate form with user data
        document.getElementById('userId').value = currentUserId;
        document.getElementById('firstName').value = user.firstName || '';
        document.getElementById('lastName').value = user.lastName || '';
        document.getElementById('email').value = user.email || '';
        document.getElementById('phone').value = user.phone || '';
        document.getElementById('employeeId').value = user.employeeId || '';
        document.getElementById('dateOfJoin').value = user.dateOfJoin || '';
        document.getElementById('position').value = user.position || '';
        document.getElementById('contractType').value = user.contractType || '';
        document.getElementById('status').value = user.status || '';
        document.getElementById('username').value = user.username || '';
        document.getElementById('accessLevel').value = user.accessLevel || '';
        document.getElementById('notes').value = user.notes || '';

        // Load existing ranks and populate the rank identifier dropdown
        loadExistingRanks();

        // Populate rank fields (will be overridden if user selects a different rank)
        document.getElementById('rankName').value = user.rankName || '';
        document.getElementById('branch').value = user.branch || '';
        document.getElementById('division').value = user.division || '';
        document.getElementById('section').value = user.section || '';
        document.getElementById('team').value = user.team || '';
        document.getElementById('rankSupervisor').value = user.rankSupervisor || '';

        // Populate supervisor dropdowns
        populateSupervisorDropdowns();

        // Function to populate supervisor dropdowns
        function populateSupervisorDropdowns() {
            // Get existing users and supervisor lists from localStorage
            const allUsers = JSON.parse(localStorage.getItem('users')) || [];
            const supervisorLists = JSON.parse(localStorage.getItem('supervisorLists')) || {
                appraisingOfficers: [],
                countersigningOfficers: []
            };

            // Get the dropdown elements
            const appraisingOfficerDropdown = document.getElementById('appraisingOfficer');
            const countersigningOfficerDropdown = document.getElementById('countersigningOfficer');

            // Store the currently selected values (if any)
            const selectedAppraising = appraisingOfficerDropdown.value;
            const selectedCountersigning = countersigningOfficerDropdown.value;

            // Clear existing options except the first one
            while (appraisingOfficerDropdown.options.length > 1) {
                appraisingOfficerDropdown.remove(1);
            }

            while (countersigningOfficerDropdown.options.length > 1) {
                countersigningOfficerDropdown.remove(1);
            }

            // Add options for appraising officers from the managed list
            supervisorLists.appraisingOfficers.forEach(username => {
                const otherUser = allUsers.find(u => u.username === username);
                if (otherUser && otherUser.status === 'active' && otherUser.username !== user.username) {
                    const option = document.createElement('option');
                    option.value = otherUser.username;
                    option.textContent = `${otherUser.firstName} ${otherUser.lastName} (${otherUser.rankName || 'No Rank'})`;
                    // Set selected if this was the previously selected option or matches the user's current appraising officer
                    if (otherUser.username === selectedAppraising || otherUser.username === user.appraisingOfficer) {
                        option.selected = true;
                    }
                    appraisingOfficerDropdown.appendChild(option);
                }
            });

            // Add options for countersigning officers from the managed list
            supervisorLists.countersigningOfficers.forEach(username => {
                const otherUser = allUsers.find(u => u.username === username);
                if (otherUser && otherUser.status === 'active' && otherUser.username !== user.username) {
                    const option = document.createElement('option');
                    option.value = otherUser.username;
                    option.textContent = `${otherUser.firstName} ${otherUser.lastName} (${otherUser.rankName || 'No Rank'})`;
                    // Set selected if this was the previously selected option or matches the user's current countersigning officer
                    if (otherUser.username === selectedCountersigning || otherUser.username === user.countersigningOfficer) {
                        option.selected = true;
                    }
                    countersigningOfficerDropdown.appendChild(option);
                }
            });
        }

        // Initialize personnel history
        if (!user.personnelHistory) {
            user.personnelHistory = [];
        }

        // Function to display personnel history
        function displayPersonnelHistory() {
            const tableBody = document.getElementById('personnelHistoryTable');

            if (!user.personnelHistory || user.personnelHistory.length === 0) {
                tableBody.innerHTML = `
                    <tr>
                        <td colspan="5" class="px-6 py-4 text-center text-sm text-gray-500">No history records found.</td>
                    </tr>
                `;
                return;
            }

            // Sort history by date (newest first)
            const sortedHistory = [...user.personnelHistory].sort((a, b) => new Date(b.date) - new Date(a.date));

            // Clear table
            tableBody.innerHTML = '';

            // Add rows
            sortedHistory.forEach((entry, index) => {
                const row = document.createElement('tr');

                // Format date
                const dateObj = new Date(entry.date);
                const formattedDate = dateObj.toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric'
                });

                // Create cells
                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${formattedDate}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full
                            ${entry.eventType === 'promotion' ? 'bg-green-100 text-green-800' :
                              entry.eventType === 'transfer' ? 'bg-blue-100 text-blue-800' :
                              'bg-purple-100 text-purple-800'}">
                            ${entry.eventType === 'newPosition' ? 'New Position' : entry.eventType.charAt(0).toUpperCase() + entry.eventType.slice(1)}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${entry.position}</td>
                    <td class="px-6 py-4 text-sm text-gray-900">${entry.details}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <button type="button" class="text-red-600 hover:text-red-900 deleteHistoryBtn" data-index="${index}">
                            Delete
                        </button>
                    </td>
                `;

                tableBody.appendChild(row);
            });

            // Add event listeners to delete buttons
            document.querySelectorAll('.deleteHistoryBtn').forEach(button => {
                button.addEventListener('click', function() {
                    const index = parseInt(this.getAttribute('data-index'));
                    deleteHistoryEntry(index);
                });
            });
        }

        // Function to add a new history entry
        function addHistoryEntry() {
            const date = document.getElementById('historyDate').value;
            const eventType = document.getElementById('historyEventType').value;
            const position = document.getElementById('historyPosition').value;
            const details = document.getElementById('historyDetails').value;

            // Validate inputs
            if (!date || !position || !eventType) {
                alert('Please fill in all required fields (Date, Event Type, and Position).');
                return;
            }

            // Create new entry
            const newEntry = {
                date: date,
                eventType: eventType,
                position: position,
                details: details
            };

            // Add to user's history
            user.personnelHistory.push(newEntry);

            // Update the user in localStorage
            const userIndex = users.findIndex(u => u.username === user.username);
            if (userIndex !== -1) {
                users[userIndex] = user;
                localStorage.setItem('users', JSON.stringify(users));
            }

            // Clear form
            document.getElementById('historyDate').value = '';
            document.getElementById('historyPosition').value = '';
            document.getElementById('historyDetails').value = '';

            // Refresh display
            displayPersonnelHistory();
        }

        // Function to delete a history entry
        function deleteHistoryEntry(index) {
            // Confirm deletion
            if (!confirm('Are you sure you want to delete this history entry?')) {
                return;
            }

            // Remove entry
            user.personnelHistory.splice(index, 1);

            // Update the user in localStorage
            const userIndex = users.findIndex(u => u.username === user.username);
            if (userIndex !== -1) {
                users[userIndex] = user;
                localStorage.setItem('users', JSON.stringify(users));
            }

            // Refresh display
            displayPersonnelHistory();
        }

        // Initialize personnel history display
        displayPersonnelHistory();

        // Add event listener to the Add Entry button
        document.getElementById('addHistoryEntry').addEventListener('click', addHistoryEntry);

        // Form submission handler
        document.getElementById('modifyAccountForm').addEventListener('submit', function(e) {
            e.preventDefault();

            // Get form data
            const formData = new FormData(this);
            const updatedUserData = {};

            // Convert FormData to object
            for (let [key, value] of formData.entries()) {
                updatedUserData[key] = value;
            }

            // Get existing users from localStorage
            const existingUsers = JSON.parse(localStorage.getItem('users')) || [];

            // Make sure the user ID is a string for comparison
            if (typeof updatedUserData.userId !== 'string') {
                updatedUserData.userId = String(updatedUserData.userId);
            }

            // Find the user index
            const userIndex = existingUsers.findIndex(u => String(u.id) === updatedUserData.userId);

            console.log('User ID from form:', updatedUserData.userId);
            console.log('User index found:', userIndex);

            if (userIndex !== -1) {
                // Preserve personnel history from the current user object
                updatedUserData.personnelHistory = user.personnelHistory || [];

                // Update user data
                existingUsers[userIndex] = updatedUserData;

                // Save to localStorage
                localStorage.setItem('users', JSON.stringify(existingUsers));

                // Show success modal
                document.getElementById('successModal').classList.remove('hidden');
            } else {
                alert('Error updating user. User not found.');
            }
        });

        // Close modal and redirect to user list
        function closeModal() {
            document.getElementById('successModal').classList.add('hidden');
            window.location.href = 'index.html';
        }
    </script>
</body>
</html>

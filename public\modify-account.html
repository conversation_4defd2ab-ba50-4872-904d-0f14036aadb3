<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modify User Account - OFCA Portal</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="css/ofca-styles.css">
    <script src="js/header.js"></script>
    <script src="js/footer.js"></script>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="p-8">
    <div class="bg-white p-8 rounded-lg shadow-lg w-full max-w-4xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold">Modify User Account</h1>
            <a href="index.html" class="text-indigo-600 hover:text-indigo-900">Back to User List</a>
        </div>

        <form id="modifyAccountForm" class="space-y-6">
            <!-- Hidden field for user ID -->
            <input type="hidden" id="userId" name="userId">

            <!-- Personal Information Section -->
            <div>
                <h2 class="text-lg font-medium text-gray-900 border-b pb-2">Personal Information</h2>
                <div class="mt-4 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                    <div class="sm:col-span-3">
                        <label for="firstName" class="block text-sm font-medium text-gray-700">First Name</label>
                        <input type="text" name="firstName" id="firstName" required class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>

                    <div class="sm:col-span-3">
                        <label for="lastName" class="block text-sm font-medium text-gray-700">Last Name</label>
                        <input type="text" name="lastName" id="lastName" required class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>

                    <div class="sm:col-span-3">
                        <label for="email" class="block text-sm font-medium text-gray-700">Email Address</label>
                        <input type="email" name="email" id="email" required class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>

                    <div class="sm:col-span-3">
                        <label for="phone" class="block text-sm font-medium text-gray-700">Phone Number</label>
                        <input type="tel" name="phone" id="phone" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>
                </div>
            </div>

            <!-- Employment Information Section -->
            <div>
                <h2 class="text-lg font-medium text-gray-900 border-b pb-2">Employment Information</h2>
                <div class="mt-4 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                    <div class="sm:col-span-3">
                        <label for="startDate" class="block text-sm font-medium text-gray-700">Start Date</label>
                        <input type="date" name="startDate" id="startDate" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>
                </div>
            </div>

            <!-- Rank Section -->
            <div>
                <h2 class="text-lg font-medium text-gray-900 border-b pb-2">Rank</h2>
                <div class="mt-4 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                    <div class="sm:col-span-3">
                        <label for="rankIdentifier" class="block text-sm font-medium text-gray-700">Rank Identifier</label>
                        <input type="text" name="rankIdentifier" id="rankIdentifier" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>

                    <div class="sm:col-span-3">
                        <label for="rankName" class="block text-sm font-medium text-gray-700">Rank Name</label>
                        <select id="rankName" name="rankName" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" onchange="toggleRankPermissionsVisibility()">
                            <option value="">Select Rank Name</option>
                            <option value="Senior User">Senior User</option>
                            <option value="Junior User">Junior User</option>
                            <option value="System Admin">System Admin</option>
                            <option value="Section Admin">Section Admin</option>
                            <option value="Division Admin">Division Admin</option>
                            <option value="PR Officer">PR Officer</option>
                        </select>
                    </div>

                    <div class="sm:col-span-3">
                        <label for="branch" class="block text-sm font-medium text-gray-700">Branch</label>
                        <input type="text" name="branch" id="branch" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>

                    <div class="sm:col-span-3">
                        <label for="division" class="block text-sm font-medium text-gray-700">Division</label>
                        <input type="text" name="division" id="division" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>

                    <div class="sm:col-span-3">
                        <label for="section" class="block text-sm font-medium text-gray-700">Section</label>
                        <input type="text" name="section" id="section" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>

                    <div class="sm:col-span-3">
                        <label for="team" class="block text-sm font-medium text-gray-700">Team</label>
                        <input type="text" name="team" id="team" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>
                </div>
            </div>

            <!-- Account Details Section -->
            <div>
                <h2 class="text-lg font-medium text-gray-900 border-b pb-2">Account Details</h2>
                <div class="mt-4 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                    <div class="sm:col-span-3">
                        <label for="username" class="block text-sm font-medium text-gray-700">Username</label>
                        <input type="text" name="username" id="username" required readonly class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-100 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                        <p class="mt-1 text-xs text-gray-500">Username cannot be changed</p>
                    </div>

                    <div class="sm:col-span-3">
                        <label for="status" class="block text-sm font-medium text-gray-700">Status</label>
                        <select id="status" name="status" required class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                            <option value="">Select Status</option>
                            <option value="active">Active</option>
                            <option value="pending">Pending</option>
                            <option value="resigned">Resigned</option>
                            <option value="disabled">Disabled</option>
                        </select>
                    </div>

                    <div class="sm:col-span-3">
                        <label for="accessLevel" class="block text-sm font-medium text-gray-700">Access Level</label>
                        <select id="accessLevel" name="accessLevel" required class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                            <option value="">Select Access Level</option>
                            <option value="Basic">Basic</option>
                            <option value="Standard">Standard</option>
                            <option value="Advanced">Advanced</option>
                            <option value="Full">Full</option>
                        </select>
                    </div>

                    <div class="sm:col-span-6">
                        <label for="notes" class="block text-sm font-medium text-gray-700">Additional Notes</label>
                        <textarea id="notes" name="notes" rows="3" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"></textarea>
                    </div>
                </div>
            </div>

            <div class="flex justify-end space-x-3">
                <button type="button" onclick="window.location.href='index.html'" class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Cancel
                </button>
                <button type="submit" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Save Changes
                </button>
            </div>
        </form>
    </div>

    <!-- Success Modal -->
    <div id="successModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden">
        <div class="bg-white p-6 rounded-lg shadow-xl max-w-md w-full">
            <div class="text-center">
                <svg class="mx-auto h-12 w-12 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <h3 class="mt-2 text-lg font-medium text-gray-900">Account Updated Successfully</h3>
                <p class="mt-1 text-sm text-gray-500">
                    The user account has been updated successfully.
                </p>
                <div class="mt-4">
                    <button type="button" onclick="closeModal()" class="inline-flex justify-center px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700 focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-indigo-500">
                        Return to User List
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Function to toggle visibility of rank permissions section based on rank name
        function toggleRankPermissionsVisibility() {
            const rankNameInput = document.getElementById('rankName');
            // If we add a permissions section later, we can toggle it here based on rank name
        }

        // Get user data from URL parameter
        const urlParams = new URLSearchParams(window.location.search);
        const username = urlParams.get('user');

        // Get users from localStorage
        const users = JSON.parse(localStorage.getItem('users')) || [];

        // Find the user to modify
        const user = users.find(u => u.username === username);

        // If user not found, redirect to index
        if (!user) {
            alert('User not found');
            window.location.href = 'index.html';
        }

        // Store the user ID in a global variable for later use
        const currentUserId = user.id;

        // Populate form with user data
        document.getElementById('userId').value = currentUserId;
        document.getElementById('firstName').value = user.firstName || '';
        document.getElementById('lastName').value = user.lastName || '';
        document.getElementById('email').value = user.email || '';
        document.getElementById('phone').value = user.phone || '';
        document.getElementById('startDate').value = user.startDate || '';
        document.getElementById('username').value = user.username || '';
        document.getElementById('status').value = user.status || '';
        document.getElementById('accessLevel').value = user.accessLevel || '';
        document.getElementById('notes').value = user.notes || '';

        // Populate rank fields
        document.getElementById('rankIdentifier').value = user.rankIdentifier || '';
        document.getElementById('rankName').value = user.rankName || '';
        document.getElementById('branch').value = user.branch || '';
        document.getElementById('division').value = user.division || '';
        document.getElementById('section').value = user.section || '';
        document.getElementById('team').value = user.team || '';

        // Form submission handler
        document.getElementById('modifyAccountForm').addEventListener('submit', function(e) {
            e.preventDefault();

            // Get form data
            const formData = new FormData(this);
            const updatedUserData = {};

            // Convert FormData to object
            for (let [key, value] of formData.entries()) {
                updatedUserData[key] = value;
            }

            // Get existing users from localStorage
            const existingUsers = JSON.parse(localStorage.getItem('users')) || [];

            // Make sure the user ID is a string for comparison
            if (typeof updatedUserData.userId !== 'string') {
                updatedUserData.userId = String(updatedUserData.userId);
            }

            // Find the user index
            const userIndex = existingUsers.findIndex(u => String(u.id) === updatedUserData.userId);

            console.log('User ID from form:', updatedUserData.userId);
            console.log('User index found:', userIndex);

            if (userIndex !== -1) {
                // Update user data
                existingUsers[userIndex] = updatedUserData;

                // Save to localStorage
                localStorage.setItem('users', JSON.stringify(existingUsers));

                // Show success modal
                document.getElementById('successModal').classList.remove('hidden');
            } else {
                alert('Error updating user. User not found.');
            }
        });

        // Close modal and redirect to user list
        function closeModal() {
            document.getElementById('successModal').classList.add('hidden');
            window.location.href = 'index.html';
        }
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Task Management - OFCA Portal</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="css/ofca-styles.css">
    <script src="js/header.js"></script>
    <script src="js/footer.js"></script>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="p-8">
        <div class="bg-white p-8 rounded-lg shadow-lg w-full max-w-7xl mx-auto">
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-2xl font-bold">Task Management</h1>
                <div class="flex space-x-3">
                    <button id="createTaskBtn" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Create New Task
                    </button>
                    <a href="index.html" class="text-indigo-600 hover:text-indigo-900">Back to Dashboard</a>
                </div>
            </div>

            <!-- Tab Navigation -->
            <div class="border-b border-gray-200 mb-6">
                <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                    <button id="activeTasksTab" class="border-indigo-500 text-indigo-600 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" onclick="showTab('activeTasks')">
                        Active Tasks
                    </button>
                    <button id="completedTasksTab" class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" onclick="showTab('completedTasks')">
                        Completed Tasks
                    </button>
                </nav>
            </div>

            <!-- Search and Filter Section -->
            <div class="mb-6 bg-gray-50 p-4 rounded-lg">
                <h2 class="text-lg font-medium text-gray-900 mb-4">Search and Filter</h2>
                <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                    <div>
                        <label for="taskSearch" class="block text-sm font-medium text-gray-700">Search Tasks</label>
                        <input type="text" id="taskSearch" placeholder="Search by title or description..." class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>
                    <div>
                        <label for="statusFilter" class="block text-sm font-medium text-gray-700">Status</label>
                        <select id="statusFilter" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                            <option value="all">All Statuses</option>
                            <option value="new">New</option>
                            <option value="pending">Pending</option>
                            <option value="in_progress">In Progress</option>
                            <option value="completed">Completed</option>
                            <option value="overdue">Overdue</option>
                        </select>
                    </div>
                    <div>
                        <label for="priorityFilter" class="block text-sm font-medium text-gray-700">Priority</label>
                        <select id="priorityFilter" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                            <option value="all">All Priorities</option>
                            <option value="high">High</option>
                            <option value="medium">Medium</option>
                            <option value="low">Low</option>
                        </select>
                    </div>
                    <div>
                        <label for="dueDateFilter" class="block text-sm font-medium text-gray-700">Due Date</label>
                        <select id="dueDateFilter" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                            <option value="all">All Dates</option>
                            <option value="today">Due Today</option>
                            <option value="tomorrow">Due Tomorrow</option>
                            <option value="this_week">Due This Week</option>
                            <option value="overdue">Overdue</option>
                        </select>
                    </div>
                    <div class="flex items-end">
                        <button id="applyFilters" class="w-full px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            Apply Filters
                        </button>
                    </div>
                </div>
            </div>

            <!-- Active Tasks Tab Content -->
            <div id="activeTasksContent">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-lg font-medium text-gray-900">Active Tasks</h2>
                    <div class="flex space-x-2">
                        <button id="bulkCompleteBtn" class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                            Mark Selected as Complete
                        </button>
                        <span id="activeTaskCount" class="px-3 py-1 text-sm text-gray-600">0 tasks</span>
                    </div>
                </div>

                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <input type="checkbox" id="selectAllActive" class="rounded">
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Title</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Priority</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Due Date</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody id="activeTasksTableBody" class="bg-white divide-y divide-gray-200">
                            <!-- Active tasks will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Completed Tasks Tab Content -->
            <div id="completedTasksContent" class="hidden">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-lg font-medium text-gray-900">Completed Tasks</h2>
                    <div class="flex space-x-2">
                        <button id="bulkDeleteBtn" class="px-3 py-1 text-sm border border-red-300 text-red-600 rounded-md hover:bg-red-50 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                            Delete Selected
                        </button>
                        <span id="completedTaskCount" class="px-3 py-1 text-sm text-gray-600">0 tasks</span>
                    </div>
                </div>

                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <input type="checkbox" id="selectAllCompleted" class="rounded">
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Title</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Priority</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Completed Date</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody id="completedTasksTableBody" class="bg-white divide-y divide-gray-200">
                            <!-- Completed tasks will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Create/Edit Task Modal -->
    <div id="taskModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden">
        <div class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900" id="modalTitle">Create New Task</h3>
                <button type="button" id="closeTaskModal" class="text-gray-400 hover:text-gray-500">
                    <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <form id="taskForm" class="space-y-4">
                <input type="hidden" id="taskId" name="taskId">

                <div>
                    <label for="taskTitle" class="block text-sm font-medium text-gray-700">Title *</label>
                    <input type="text" id="taskTitle" name="title" required class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                </div>

                <div>
                    <label for="taskDescription" class="block text-sm font-medium text-gray-700">Description</label>
                    <textarea id="taskDescription" name="description" rows="4" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"></textarea>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="taskPriority" class="block text-sm font-medium text-gray-700">Priority *</label>
                        <select id="taskPriority" name="priority" required class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                            <option value="low">Low</option>
                            <option value="medium" selected>Medium</option>
                            <option value="high">High</option>
                        </select>
                    </div>

                    <div>
                        <label for="taskDueDate" class="block text-sm font-medium text-gray-700">Due Date *</label>
                        <input type="date" id="taskDueDate" name="dueDate" required class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>
                </div>

                <!-- Hidden status field for edit mode only -->
                <div id="statusFieldContainer" class="hidden">
                    <label for="taskStatus" class="block text-sm font-medium text-gray-700">Status</label>
                    <select id="taskStatus" name="status" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                        <option value="new">New</option>
                        <option value="pending">Pending</option>
                        <option value="in_progress">In Progress</option>
                        <option value="completed">Completed</option>
                    </select>
                </div>

                <div class="flex justify-end space-x-3 pt-4">
                    <button type="button" id="cancelTask" class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                        Cancel
                    </button>
                    <button type="submit" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700">
                        <span id="submitButtonText">Create Task</span>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Task Detail Modal -->
    <div id="taskDetailModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden">
        <div class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">Task Details</h3>
                <button type="button" id="closeDetailModal" class="text-gray-400 hover:text-gray-500">
                    <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <div id="taskDetailContent" class="space-y-4">
                <!-- Task details will be populated by JavaScript -->
            </div>

            <div class="flex justify-end space-x-3 pt-4">
                <button type="button" id="editTaskFromDetail" class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                    Edit Task
                </button>
                <button type="button" id="closeDetailModalBtn" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700">
                    Close
                </button>
            </div>
        </div>
    </div>

    <!-- Success Modal -->
    <div id="successModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden">
        <div class="bg-white p-6 rounded-lg shadow-xl max-w-md w-full">
            <div class="text-center">
                <svg class="mx-auto h-12 w-12 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <h3 class="mt-2 text-lg font-medium text-gray-900" id="successTitle">Success</h3>
                <p class="mt-1 text-sm text-gray-500" id="successMessage">Operation completed successfully.</p>
                <div class="mt-4">
                    <button type="button" id="closeSuccessModal" class="inline-flex justify-center px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700">
                        OK
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let tasks = [];
        let currentUser = 'current_user'; // This would be dynamic in a real application
        let currentTab = 'activeTasks';
        let editingTaskId = null;

        // Initialize the page
        document.addEventListener('DOMContentLoaded', function() {
            loadTasks();
            setupEventListeners();
            populateTaskTables();
            setDefaultDueDate();
        });

        // Load tasks from localStorage
        function loadTasks() {
            tasks = JSON.parse(localStorage.getItem('tasks')) || generateSampleTasks();
            localStorage.setItem('tasks', JSON.stringify(tasks));
        }

        // Generate sample tasks for demonstration
        function generateSampleTasks() {
            const sampleTasks = [
                {
                    id: 'task_001',
                    title: 'Review Monthly Reports',
                    description: 'Review and analyze monthly performance reports for Q4',
                    priority: 'high',
                    status: 'new',
                    dueDate: getDateString(3), // 3 days from now
                    createdDate: getDateString(-5),
                    createdBy: currentUser,
                    assignedTo: currentUser,
                    source: 'manual'
                },
                {
                    id: 'task_002',
                    title: 'Update System Documentation',
                    description: 'Update user manual and system documentation with latest features',
                    priority: 'medium',
                    status: 'in_progress',
                    dueDate: getDateString(7),
                    createdDate: getDateString(-3),
                    createdBy: currentUser,
                    assignedTo: currentUser,
                    source: 'notification'
                },
                {
                    id: 'task_003',
                    title: 'Prepare Budget Presentation',
                    description: 'Create presentation for next quarter budget review meeting',
                    priority: 'high',
                    status: 'completed',
                    dueDate: getDateString(-2),
                    createdDate: getDateString(-10),
                    completedDate: getDateString(-1),
                    createdBy: currentUser,
                    assignedTo: currentUser,
                    source: 'api'
                }
            ];
            return sampleTasks;
        }

        // Setup event listeners
        function setupEventListeners() {
            // Modal controls
            document.getElementById('createTaskBtn').addEventListener('click', openCreateTaskModal);
            document.getElementById('closeTaskModal').addEventListener('click', closeTaskModal);
            document.getElementById('cancelTask').addEventListener('click', closeTaskModal);
            document.getElementById('closeDetailModal').addEventListener('click', closeTaskDetailModal);
            document.getElementById('closeDetailModalBtn').addEventListener('click', closeTaskDetailModal);
            document.getElementById('editTaskFromDetail').addEventListener('click', editTaskFromDetail);
            document.getElementById('closeSuccessModal').addEventListener('click', closeSuccessModal);

            // Form submission
            document.getElementById('taskForm').addEventListener('submit', handleTaskSubmit);

            // Filter and search
            document.getElementById('applyFilters').addEventListener('click', applyFilters);
            document.getElementById('taskSearch').addEventListener('input', applyFilters);

            // Bulk operations
            document.getElementById('selectAllActive').addEventListener('change', toggleSelectAllActive);
            document.getElementById('selectAllCompleted').addEventListener('change', toggleSelectAllCompleted);
            document.getElementById('bulkCompleteBtn').addEventListener('click', bulkCompleteSelected);
            document.getElementById('bulkDeleteBtn').addEventListener('click', bulkDeleteSelected);
        }

        // Tab management
        function showTab(tabName) {
            currentTab = tabName;

            // Hide all tab contents
            document.getElementById('activeTasksContent').classList.add('hidden');
            document.getElementById('completedTasksContent').classList.add('hidden');

            // Remove active class from all tabs
            document.getElementById('activeTasksTab').classList.remove('border-indigo-500', 'text-indigo-600');
            document.getElementById('activeTasksTab').classList.add('border-transparent', 'text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300');
            document.getElementById('completedTasksTab').classList.remove('border-indigo-500', 'text-indigo-600');
            document.getElementById('completedTasksTab').classList.add('border-transparent', 'text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300');

            // Show selected tab content and mark tab as active
            if (tabName === 'activeTasks') {
                document.getElementById('activeTasksContent').classList.remove('hidden');
                document.getElementById('activeTasksTab').classList.remove('border-transparent', 'text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300');
                document.getElementById('activeTasksTab').classList.add('border-indigo-500', 'text-indigo-600');
            } else if (tabName === 'completedTasks') {
                document.getElementById('completedTasksContent').classList.remove('hidden');
                document.getElementById('completedTasksTab').classList.remove('border-transparent', 'text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300');
                document.getElementById('completedTasksTab').classList.add('border-indigo-500', 'text-indigo-600');
            }

            populateTaskTables();
        }

        // Populate task tables
        function populateTaskTables() {
            populateActiveTasksTable();
            populateCompletedTasksTable();
            updateTaskCounts();
        }

        // Populate active tasks table
        function populateActiveTasksTable() {
            const tableBody = document.getElementById('activeTasksTableBody');
            tableBody.innerHTML = '';

            const activeTasks = getFilteredTasks().filter(task => task.status !== 'completed');

            activeTasks.forEach(task => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap">
                        <input type="checkbox" class="task-checkbox-active rounded" data-task-id="${task.id}">
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium text-gray-900 cursor-pointer hover:text-indigo-600" onclick="viewTaskDetail('${task.id}')">
                            ${task.title}
                        </div>
                        <div class="text-sm text-gray-500 truncate max-w-xs">
                            ${task.description || 'No description'}
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getPriorityColor(task.priority)}">
                            ${task.priority.charAt(0).toUpperCase() + task.priority.slice(1)}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusColor(task.status)}">
                            ${getStatusText(task.status)}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div class="${isOverdue(task.dueDate) ? 'text-red-600 font-medium' : ''}">
                            ${formatDate(task.dueDate)}
                            ${isOverdue(task.dueDate) ? '<br><span class="text-xs">(Overdue)</span>' : ''}
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        ${formatDate(task.createdDate)}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                        <button onclick="viewTaskDetail('${task.id}')" class="text-indigo-600 hover:text-indigo-900">View</button>
                        <button onclick="editTask('${task.id}')" class="text-blue-600 hover:text-blue-900">Edit</button>
                        <button onclick="completeTask('${task.id}')" class="text-green-600 hover:text-green-900">Complete</button>
                        <button onclick="deleteTask('${task.id}')" class="text-red-600 hover:text-red-900">Delete</button>
                    </td>
                `;
                tableBody.appendChild(row);
            });

            // Update bulk action button state
            updateBulkActionButtons();
        }

        // Populate completed tasks table
        function populateCompletedTasksTable() {
            const tableBody = document.getElementById('completedTasksTableBody');
            tableBody.innerHTML = '';

            const completedTasks = getFilteredTasks().filter(task => task.status === 'completed');

            completedTasks.forEach(task => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap">
                        <input type="checkbox" class="task-checkbox-completed rounded" data-task-id="${task.id}">
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium text-gray-900 cursor-pointer hover:text-indigo-600" onclick="viewTaskDetail('${task.id}')">
                            ${task.title}
                        </div>
                        <div class="text-sm text-gray-500 truncate max-w-xs">
                            ${task.description || 'No description'}
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getPriorityColor(task.priority)}">
                            ${task.priority.charAt(0).toUpperCase() + task.priority.slice(1)}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ${formatDate(task.completedDate)}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                        <button onclick="viewTaskDetail('${task.id}')" class="text-indigo-600 hover:text-indigo-900">View</button>
                        <button onclick="deleteTask('${task.id}')" class="text-red-600 hover:text-red-900">Delete</button>
                    </td>
                `;
                tableBody.appendChild(row);
            });

            // Update bulk action button state
            updateBulkActionButtons();
        }

        // Get filtered tasks based on current filters
        function getFilteredTasks() {
            const searchTerm = document.getElementById('taskSearch').value.toLowerCase();
            const statusFilter = document.getElementById('statusFilter').value;
            const priorityFilter = document.getElementById('priorityFilter').value;
            const dueDateFilter = document.getElementById('dueDateFilter').value;

            return tasks.filter(task => {
                // Search filter
                const matchesSearch = task.title.toLowerCase().includes(searchTerm) ||
                                    (task.description && task.description.toLowerCase().includes(searchTerm));

                // Status filter
                let matchesStatus = statusFilter === 'all';
                if (!matchesStatus) {
                    if (statusFilter === 'overdue') {
                        matchesStatus = task.status !== 'completed' && isOverdue(task.dueDate);
                    } else {
                        matchesStatus = task.status === statusFilter;
                    }
                }

                // Priority filter
                const matchesPriority = priorityFilter === 'all' || task.priority === priorityFilter;

                // Due date filter
                let matchesDueDate = dueDateFilter === 'all';
                if (!matchesDueDate) {
                    const today = new Date();
                    const taskDueDate = new Date(task.dueDate);

                    switch (dueDateFilter) {
                        case 'today':
                            matchesDueDate = isSameDay(taskDueDate, today);
                            break;
                        case 'tomorrow':
                            const tomorrow = new Date(today);
                            tomorrow.setDate(today.getDate() + 1);
                            matchesDueDate = isSameDay(taskDueDate, tomorrow);
                            break;
                        case 'this_week':
                            const weekEnd = new Date(today);
                            weekEnd.setDate(today.getDate() + 7);
                            matchesDueDate = taskDueDate >= today && taskDueDate <= weekEnd;
                            break;
                        case 'overdue':
                            matchesDueDate = isOverdue(task.dueDate) && task.status !== 'completed';
                            break;
                    }
                }

                return matchesSearch && matchesStatus && matchesPriority && matchesDueDate;
            });
        }

        // Apply filters
        function applyFilters() {
            populateTaskTables();
        }

        // Task CRUD operations
        function openCreateTaskModal() {
            editingTaskId = null;
            document.getElementById('modalTitle').textContent = 'Create New Task';
            document.getElementById('submitButtonText').textContent = 'Create Task';
            document.getElementById('taskForm').reset();
            document.getElementById('taskId').value = '';

            // Hide status field for new tasks
            document.getElementById('statusFieldContainer').classList.add('hidden');

            setDefaultDueDate();
            document.getElementById('taskModal').classList.remove('hidden');
        }

        function editTask(taskId) {
            const task = tasks.find(t => t.id === taskId);
            if (!task) return;

            editingTaskId = taskId;
            document.getElementById('modalTitle').textContent = 'Edit Task';
            document.getElementById('submitButtonText').textContent = 'Update Task';

            // Show status field for editing existing tasks
            document.getElementById('statusFieldContainer').classList.remove('hidden');

            // Populate form with task data
            document.getElementById('taskId').value = task.id;
            document.getElementById('taskTitle').value = task.title;
            document.getElementById('taskDescription').value = task.description || '';
            document.getElementById('taskPriority').value = task.priority;
            document.getElementById('taskStatus').value = task.status;
            document.getElementById('taskDueDate').value = task.dueDate;

            document.getElementById('taskModal').classList.remove('hidden');
        }

        function handleTaskSubmit(event) {
            event.preventDefault();

            const formData = new FormData(event.target);
            const taskData = {
                title: formData.get('title'),
                description: formData.get('description'),
                priority: formData.get('priority'),
                status: formData.get('status'),
                dueDate: formData.get('dueDate')
            };

            if (editingTaskId) {
                updateTask(editingTaskId, taskData);
            } else {
                createTask(taskData);
            }
        }

        function createTask(taskData) {
            const newTask = {
                id: 'task_' + Date.now(),
                ...taskData,
                status: 'new', // Default status for new tasks
                createdDate: getCurrentDateString(),
                createdBy: currentUser,
                assignedTo: currentUser,
                source: 'manual'
            };

            tasks.push(newTask);
            saveTasks();
            closeTaskModal();
            populateTaskTables();
            showSuccessMessage('Task Created', 'Task has been created successfully.');
        }

        function updateTask(taskId, taskData) {
            const taskIndex = tasks.findIndex(t => t.id === taskId);
            if (taskIndex === -1) return;

            tasks[taskIndex] = {
                ...tasks[taskIndex],
                ...taskData,
                modifiedDate: getCurrentDateString()
            };

            saveTasks();
            closeTaskModal();
            populateTaskTables();
            showSuccessMessage('Task Updated', 'Task has been updated successfully.');
        }

        function completeTask(taskId) {
            const taskIndex = tasks.findIndex(t => t.id === taskId);
            if (taskIndex === -1) return;

            tasks[taskIndex].status = 'completed';
            tasks[taskIndex].completedDate = getCurrentDateString();

            saveTasks();
            populateTaskTables();
            showSuccessMessage('Task Completed', 'Task has been marked as completed.');
        }



        function deleteTask(taskId) {
            if (!confirm('Are you sure you want to delete this task?')) return;

            tasks = tasks.filter(t => t.id !== taskId);
            saveTasks();
            populateTaskTables();
            showSuccessMessage('Task Deleted', 'Task has been deleted successfully.');
        }

        // Utility functions
        function saveTasks() {
            localStorage.setItem('tasks', JSON.stringify(tasks));
        }

        function setDefaultDueDate() {
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            document.getElementById('taskDueDate').value = tomorrow.toISOString().split('T')[0];
        }

        function getCurrentDateString() {
            return new Date().toISOString().split('T')[0];
        }

        function getDateString(daysFromNow) {
            const date = new Date();
            date.setDate(date.getDate() + daysFromNow);
            return date.toISOString().split('T')[0];
        }

        function formatDate(dateString) {
            return new Date(dateString).toLocaleDateString();
        }

        function isOverdue(dueDate) {
            const today = new Date();
            const due = new Date(dueDate);
            today.setHours(0, 0, 0, 0);
            due.setHours(0, 0, 0, 0);
            return due < today;
        }

        function isSameDay(date1, date2) {
            return date1.toDateString() === date2.toDateString();
        }



        function getPriorityColor(priority) {
            switch (priority) {
                case 'high': return 'bg-red-100 text-red-800';
                case 'medium': return 'bg-yellow-100 text-yellow-800';
                case 'low': return 'bg-green-100 text-green-800';
                default: return 'bg-gray-100 text-gray-800';
            }
        }

        function getStatusColor(status) {
            switch (status) {
                case 'new': return 'bg-purple-100 text-purple-800';
                case 'pending': return 'bg-gray-100 text-gray-800';
                case 'in_progress': return 'bg-blue-100 text-blue-800';
                case 'completed': return 'bg-green-100 text-green-800';
                default: return 'bg-gray-100 text-gray-800';
            }
        }

        function getStatusText(status) {
            switch (status) {
                case 'new': return 'New';
                case 'in_progress': return 'In Progress';
                case 'pending': return 'Pending';
                case 'completed': return 'Completed';
                default: return status;
            }
        }

        function updateTaskCounts() {
            const activeTasks = tasks.filter(task => task.status !== 'completed');
            const completedTasks = tasks.filter(task => task.status === 'completed');

            document.getElementById('activeTaskCount').textContent = `${activeTasks.length} task${activeTasks.length !== 1 ? 's' : ''}`;
            document.getElementById('completedTaskCount').textContent = `${completedTasks.length} task${completedTasks.length !== 1 ? 's' : ''}`;
        }

        // Modal management
        function closeTaskModal() {
            document.getElementById('taskModal').classList.add('hidden');
            editingTaskId = null;
        }

        function closeTaskDetailModal() {
            document.getElementById('taskDetailModal').classList.add('hidden');
        }

        function closeSuccessModal() {
            document.getElementById('successModal').classList.add('hidden');
        }

        function showSuccessMessage(title, message) {
            document.getElementById('successTitle').textContent = title;
            document.getElementById('successMessage').textContent = message;
            document.getElementById('successModal').classList.remove('hidden');
        }

        // Task detail view
        function viewTaskDetail(taskId) {
            const task = tasks.find(t => t.id === taskId);
            if (!task) return;

            const detailContent = document.getElementById('taskDetailContent');
            detailContent.innerHTML = `
                <div class="space-y-4">
                    <div>
                        <h4 class="text-lg font-medium text-gray-900">${task.title}</h4>
                        <p class="text-sm text-gray-500">Task ID: ${task.id}</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700">Description</label>
                        <p class="mt-1 text-sm text-gray-900">${task.description || 'No description provided'}</p>
                    </div>

                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Priority</label>
                            <span class="mt-1 px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getPriorityColor(task.priority)}">
                                ${task.priority.charAt(0).toUpperCase() + task.priority.slice(1)}
                            </span>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Status</label>
                            <span class="mt-1 px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusColor(task.status)}">
                                ${getStatusText(task.status)}
                            </span>
                        </div>
                    </div>

                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Due Date</label>
                            <p class="mt-1 text-sm text-gray-900 ${isOverdue(task.dueDate) && task.status !== 'completed' ? 'text-red-600 font-medium' : ''}">
                                ${formatDate(task.dueDate)}
                                ${isOverdue(task.dueDate) && task.status !== 'completed' ? ' (Overdue)' : ''}
                            </p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Created Date</label>
                            <p class="mt-1 text-sm text-gray-900">${formatDate(task.createdDate)}</p>
                        </div>
                    </div>

                    ${task.completedDate ? `
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Completed Date</label>
                            <p class="mt-1 text-sm text-gray-900">${formatDate(task.completedDate)}</p>
                        </div>
                    ` : ''}

                    <div>
                        <label class="block text-sm font-medium text-gray-700">Source</label>
                        <p class="mt-1 text-sm text-gray-900">${task.source === 'manual' ? 'Manually Created' : task.source === 'notification' ? 'From Notification' : 'API Integration'}</p>
                    </div>
                </div>
            `;

            // Set up edit button
            document.getElementById('editTaskFromDetail').onclick = () => {
                closeTaskDetailModal();
                editTask(taskId);
            };

            document.getElementById('taskDetailModal').classList.remove('hidden');
        }

        function editTaskFromDetail() {
            // This function is set up dynamically in viewTaskDetail
        }

        // Bulk operations
        function toggleSelectAllActive() {
            const selectAll = document.getElementById('selectAllActive');
            const checkboxes = document.querySelectorAll('.task-checkbox-active');

            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
            });

            updateBulkActionButtons();
        }

        function toggleSelectAllCompleted() {
            const selectAll = document.getElementById('selectAllCompleted');
            const checkboxes = document.querySelectorAll('.task-checkbox-completed');

            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
            });

            updateBulkActionButtons();
        }

        function updateBulkActionButtons() {
            const activeSelected = document.querySelectorAll('.task-checkbox-active:checked').length;
            const completedSelected = document.querySelectorAll('.task-checkbox-completed:checked').length;

            document.getElementById('bulkCompleteBtn').disabled = activeSelected === 0;
            document.getElementById('bulkDeleteBtn').disabled = completedSelected === 0;
        }

        function bulkCompleteSelected() {
            const selectedCheckboxes = document.querySelectorAll('.task-checkbox-active:checked');
            const selectedTaskIds = Array.from(selectedCheckboxes).map(cb => cb.dataset.taskId);

            if (selectedTaskIds.length === 0) return;

            if (!confirm(`Are you sure you want to mark ${selectedTaskIds.length} task(s) as completed?`)) return;

            selectedTaskIds.forEach(taskId => {
                const taskIndex = tasks.findIndex(t => t.id === taskId);
                if (taskIndex !== -1) {
                    tasks[taskIndex].status = 'completed';
                    tasks[taskIndex].completedDate = getCurrentDateString();
                }
            });

            saveTasks();
            populateTaskTables();
            showSuccessMessage('Tasks Completed', `${selectedTaskIds.length} task(s) have been marked as completed.`);
        }

        function bulkDeleteSelected() {
            const selectedCheckboxes = document.querySelectorAll('.task-checkbox-completed:checked');
            const selectedTaskIds = Array.from(selectedCheckboxes).map(cb => cb.dataset.taskId);

            if (selectedTaskIds.length === 0) return;

            if (!confirm(`Are you sure you want to delete ${selectedTaskIds.length} task(s)? This action cannot be undone.`)) return;

            tasks = tasks.filter(task => !selectedTaskIds.includes(task.id));

            saveTasks();
            populateTaskTables();
            showSuccessMessage('Tasks Deleted', `${selectedTaskIds.length} task(s) have been deleted.`);
        }

        // API integration function (for external systems)
        function createTaskFromAPI(taskData) {
            const newTask = {
                id: 'api_task_' + Date.now(),
                title: taskData.title,
                description: taskData.description || '',
                priority: taskData.priority || 'medium',
                status: taskData.status || 'new',
                dueDate: taskData.dueDate,
                createdDate: getCurrentDateString(),
                createdBy: taskData.createdBy || 'system',
                assignedTo: taskData.assignedTo || currentUser,
                source: 'api'
            };

            tasks.push(newTask);
            saveTasks();

            return newTask;
        }

        // Notification integration function
        function createTaskFromNotification(notificationData) {
            const newTask = {
                id: 'notif_task_' + Date.now(),
                title: notificationData.title,
                description: notificationData.description || '',
                priority: notificationData.priority || 'medium',
                status: 'new',
                dueDate: notificationData.dueDate,
                createdDate: getCurrentDateString(),
                createdBy: 'system',
                assignedTo: currentUser,
                source: 'notification',
                notificationId: notificationData.id
            };

            tasks.push(newTask);
            saveTasks();

            return newTask;
        }

        // Event listeners for checkboxes (for bulk operations)
        document.addEventListener('change', function(event) {
            if (event.target.classList.contains('task-checkbox-active') ||
                event.target.classList.contains('task-checkbox-completed')) {
                updateBulkActionButtons();
            }
        });
    </script>
</body>
</html>

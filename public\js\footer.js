// Create and insert the OFCA footer
function createFooter() {
    // Create footer container
    const footer = document.createElement('div');
    footer.className = 'ofca-footer bg-gray-800 text-white text-xs py-2 px-4 flex justify-between items-center';
    
    // Create left section with OFCA logo
    const leftSection = document.createElement('div');
    leftSection.className = 'flex items-center';
    
    // OFCA Logo
    const logo = document.createElement('img');
    logo.src = 'https://placeholder.com/wp-content/uploads/2018/10/placeholder.com-logo1.png'; // Replace with actual logo
    logo.alt = 'OFCA Logo';
    logo.className = 'h-6 mr-3';
    leftSection.appendChild(logo);
    
    // Contact information
    const contactInfo = document.createElement('div');
    contactInfo.textContent = 'For enquiry, please contact IT team: 2345678';
    leftSection.appendChild(contactInfo);
    
    // Create center section with important notice
    const centerSection = document.createElement('div');
    centerSection.className = 'mx-4';
    
    // Important Notice link
    const importantNotice = document.createElement('a');
    importantNotice.href = '#';
    importantNotice.className = 'hover:underline';
    importantNotice.textContent = 'Important Notice';
    centerSection.appendChild(importantNotice);
    
    // Separator
    const separator1 = document.createElement('span');
    separator1.className = 'mx-2';
    separator1.textContent = '|';
    centerSection.appendChild(separator1);
    
    // Terms & Conditions link
    const termsLink = document.createElement('a');
    termsLink.href = '#';
    termsLink.className = 'hover:underline';
    termsLink.textContent = 'Terms & Conditions';
    centerSection.appendChild(termsLink);
    
    // Create right section with login info and copyright
    const rightSection = document.createElement('div');
    rightSection.className = 'flex items-center';
    
    // Last login info
    const lastLogin = document.createElement('span');
    lastLogin.textContent = 'Last Login: 05/10/2024 11:22:33';
    rightSection.appendChild(lastLogin);
    
    // Separator
    const separator2 = document.createElement('span');
    separator2.className = 'mx-2';
    separator2.textContent = '|';
    rightSection.appendChild(separator2);
    
    // Copyright info
    const copyright = document.createElement('span');
    copyright.textContent = '2024 © Office of the Communications Authority';
    rightSection.appendChild(copyright);
    
    // Assemble footer
    footer.appendChild(leftSection);
    footer.appendChild(centerSection);
    footer.appendChild(rightSection);
    
    // Add orange/yellow gradient bar at the top of the footer
    const gradientBar = document.createElement('div');
    gradientBar.className = 'h-1 bg-gradient-to-r from-yellow-500 to-orange-500';
    
    // Create a wrapper to hold both the gradient bar and the footer
    const footerWrapper = document.createElement('div');
    footerWrapper.className = 'ofca-footer-wrapper fixed bottom-0 left-0 right-0';
    footerWrapper.appendChild(gradientBar);
    footerWrapper.appendChild(footer);
    
    // Insert footer at the end of the body
    document.body.appendChild(footerWrapper);
    
    // Add padding to the bottom of the body to prevent content from being hidden behind the fixed footer
    const footerHeight = footerWrapper.offsetHeight;
    document.body.style.paddingBottom = `${footerHeight + 20}px`; // Add some extra padding
}

// Call the function when the DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Wait a bit to ensure the header is loaded first
    setTimeout(createFooter, 100);
});

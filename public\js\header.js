// Create and insert the OFCA header
function createHeader() {
    // Create header container
    const header = document.createElement('div');
    header.className = 'ofca-header bg-white shadow-sm flex items-center justify-between px-2 py-2';
    
    // Left section - Menu and Logo
    const leftSection = document.createElement('div');
    leftSection.className = 'flex items-center';
    
    // Menu button
    const menuButton = document.createElement('button');
    menuButton.className = 'menu-button p-2 text-gray-700 hover:bg-gray-100 rounded-md mr-2';
    menuButton.innerHTML = `
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
        </svg>
    `;
    leftSection.appendChild(menuButton);
    
    // OFCA Logo
    const logoContainer = document.createElement('div');
    logoContainer.className = 'flex items-center';
    logoContainer.innerHTML = `
        <img src="https://placeholder.com/wp-content/uploads/2018/10/placeholder.com-logo1.png" alt="OFCA Logo" class="h-8 mr-2">
        <div class="flex flex-col">
            <span class="text-xs text-gray-600">通訊事務管理局辦公室</span>
            <span class="text-xs text-gray-600">OFFICE OF THE COMMUNICATIONS AUTHORITY</span>
        </div>
    `;
    leftSection.appendChild(logoContainer);
    
    // Portal Text
    const portalText = document.createElement('div');
    portalText.className = 'ml-4 border-l pl-4';
    portalText.innerHTML = `<span class="font-bold text-gray-700">OFCA<br>PORTAL</span>`;
    leftSection.appendChild(portalText);
    
    // Center section - Search Bar
    const centerSection = document.createElement('div');
    centerSection.className = 'flex-grow mx-4';
    centerSection.innerHTML = `
        <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                </svg>
            </div>
            <input type="text" placeholder="Search" class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-gray-100 placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
        </div>
    `;
    
    // Right section - Weather, User Info, Notifications, and Time
    const rightSection = document.createElement('div');
    rightSection.className = 'flex items-center';
    
    // Weather
    const weather = document.createElement('div');
    weather.className = 'flex items-center mr-4';
    weather.innerHTML = `
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-yellow-500" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd" />
        </svg>
        <span class="ml-1 text-sm">31°C</span>
    `;
    rightSection.appendChild(weather);
    
    // User Info
    const userInfo = document.createElement('div');
    userInfo.className = 'flex items-center mr-4';
    userInfo.innerHTML = `
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" />
        </svg>
        <div class="ml-1">
            <div class="text-sm font-medium">Chun Tai Man</div>
            <div class="text-xs text-gray-500">C2367P1</div>
        </div>
    `;
    rightSection.appendChild(userInfo);
    
    // Notification Icons
    const notifications = document.createElement('div');
    notifications.className = 'flex items-center space-x-3 mr-4';
    notifications.innerHTML = `
        <!-- Red Notification -->
        <div class="relative">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500" viewBox="0 0 20 20" fill="currentColor">
                <path d="M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zM10 18a3 3 0 01-3-3h6a3 3 0 01-3 3z" />
            </svg>
            <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">1</span>
        </div>
        
        <!-- Green Notification -->
        <div class="relative">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
            </svg>
            <span class="absolute -top-1 -right-1 bg-green-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">1</span>
        </div>
        
        <!-- Other Icons -->
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M3 5a2 2 0 012-2h10a2 2 0 012 2v8a2 2 0 01-2 2h-2.22l.123.489.804.804A1 1 0 0113 18H7a1 1 0 01-.707-1.707l.804-.804L7.22 15H5a2 2 0 01-2-2V5zm5.771 7H5V5h10v7H8.771z" clip-rule="evenodd" />
        </svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd" />
        </svg>
    `;
    rightSection.appendChild(notifications);
    
    // Timestamp
    const timestamp = document.createElement('div');
    timestamp.className = 'text-right';
    timestamp.innerHTML = `
        <div id="current-date" class="text-xs text-gray-500">08/05/2024</div>
        <div id="current-time" class="text-xs text-gray-500">10:26:34</div>
    `;
    rightSection.appendChild(timestamp);
    
    // Assemble header
    header.appendChild(leftSection);
    header.appendChild(centerSection);
    header.appendChild(rightSection);
    
    // Insert header at the beginning of the body
    document.body.insertBefore(header, document.body.firstChild);
    
    // Update time function
    function updateTime() {
        const now = new Date();
        const dateElement = document.getElementById('current-date');
        const timeElement = document.getElementById('current-time');
        
        // Format date as DD/MM/YYYY
        const date = now.toLocaleDateString('en-GB', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric'
        }).replace(/\//g, '/');
        
        // Format time as HH:MM:SS
        const time = now.toLocaleTimeString('en-GB', {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
        });
        
        dateElement.textContent = date;
        timeElement.textContent = time;
    }
    
    // Update time immediately and then every second
    updateTime();
    setInterval(updateTime, 1000);
}

// Call the function when the DOM is loaded
document.addEventListener('DOMContentLoaded', createHeader);

/* OFCA Portal Styles */

/* Header Styles */
.ofca-header {
    font-family: Arial, sans-serif;
    height: 60px;
    border-bottom: 1px solid #e5e7eb;
    position: sticky;
    top: 0;
    z-index: 100;
    background-color: white;
}

/* Footer Styles */
.ofca-footer-wrapper {
    font-family: Arial, sans-serif;
    z-index: 100;
}

.ofca-footer {
    height: 30px;
    font-size: 0.75rem;
}

/* Adjust body to account for fixed header and footer */
body {
    padding-top: 0;
    margin: 0;
    min-height: 100vh;
    position: relative;
}

/* Logo styles */
.ofca-logo {
    height: 40px;
}

/* Notification badge styles */
.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: #ef4444;
    color: white;
    border-radius: 9999px;
    font-size: 0.75rem;
    height: 16px;
    width: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* User info styles */
.user-info {
    font-size: 0.875rem;
}

.user-id {
    font-size: 0.75rem;
    color: #6b7280;
}

/* Search bar styles */
.search-bar {
    background-color: #f3f4f6;
    border-radius: 0.375rem;
    border: 1px solid #e5e7eb;
}

.search-bar:focus {
    outline: none;
    border-color: #6366f1;
    box-shadow: 0 0 0 1px rgba(99, 102, 241, 0.2);
}

/* Timestamp styles */
.timestamp {
    font-size: 0.75rem;
    color: #6b7280;
    text-align: right;
}

/* Menu button styles */
.menu-button {
    color: #4b5563;
    transition: background-color 0.2s;
}

.menu-button:hover {
    background-color: #f3f4f6;
}

/* Weather icon styles */
.weather-icon {
    color: #fbbf24;
}

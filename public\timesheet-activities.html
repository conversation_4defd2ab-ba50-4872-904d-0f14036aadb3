<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Timesheet Activity Management - OFCA Portal</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="css/ofca-styles.css">
    <script src="js/header.js"></script>
    <script src="js/footer.js"></script>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="p-8">
        <div class="bg-white p-8 rounded-lg shadow-lg w-full max-w-7xl mx-auto">
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-2xl font-bold">Timesheet Activity Management</h1>
                <div class="flex space-x-3">
                    <button id="addActivityBtn" class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Add New Activity
                    </button>
                    <a href="index.html" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">Back to Dashboard</a>
                </div>
            </div>



            <!-- Activities Table -->
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Symbol</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Activity Name</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rank</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody id="activitiesTableBody" class="bg-white divide-y divide-gray-200">
                        <!-- Activities will be populated by JavaScript -->
                    </tbody>
                </table>
            </div>

            <!-- Activity Count -->
            <div class="mt-4 text-sm text-gray-500">
                Total Activities: <span id="activityCount">0</span>
            </div>
        </div>
    </div>

    <!-- Add/Edit Activity Modal -->
    <div id="activityModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50">
        <div class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-screen overflow-y-auto">
            <div class="flex justify-between items-center mb-4">
                <h3 id="modalTitle" class="text-lg font-medium text-gray-900">Add New Activity</h3>
                <button id="closeModal" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <form id="activityForm" class="space-y-4">
                <input type="hidden" id="activityId" name="activityId">

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="activitySymbol" class="block text-sm font-medium text-gray-700">Activity Symbol *</label>
                        <input type="text" id="activitySymbol" name="activitySymbol" required maxlength="10" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="e.g., ADM, TRN, PL">
                        <p class="mt-1 text-xs text-gray-500">Unique identifier (max 10 characters)</p>
                    </div>

                    <div>
                        <label for="activityName" class="block text-sm font-medium text-gray-700">Activity Name *</label>
                        <input type="text" id="activityName" name="activityName" required class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="e.g., Administrative Work">
                    </div>
                </div>

                <div>
                    <label for="activityDescription" class="block text-sm font-medium text-gray-700">Description</label>
                    <textarea id="activityDescription" name="activityDescription" rows="3" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="Detailed description of the activity"></textarea>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="activityCategory" class="block text-sm font-medium text-gray-700">Category *</label>
                        <select id="activityCategory" name="activityCategory" required class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                            <option value="">Select Category</option>
                            <option value="operational">Operational Activities</option>
                            <option value="administrative">Administrative Tasks</option>
                            <option value="training">Training & Development</option>
                            <option value="leave">Leave & Absence</option>
                            <option value="special">Special Assignments</option>
                            <option value="project">Project Work</option>
                            <option value="meeting">Meetings & Conferences</option>
                        </select>
                    </div>

                    <div>
                        <label for="activityRank" class="block text-sm font-medium text-gray-700">Rank *</label>
                        <select id="activityRank" name="activityRank" required class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                            <option value="">Select Rank</option>
                            <option value="All Ranks">All Ranks</option>
                            <!-- Rank options will be populated dynamically -->
                        </select>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="activityStatus" class="block text-sm font-medium text-gray-700">Status</label>
                        <select id="activityStatus" name="activityStatus" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                        </select>
                    </div>

                    <div>
                        <label for="activityPriority" class="block text-sm font-medium text-gray-700">Priority</label>
                        <select id="activityPriority" name="activityPriority" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                            <option value="normal">Normal</option>
                            <option value="high">High</option>
                            <option value="low">Low</option>
                        </select>
                    </div>
                </div>

                <div class="flex justify-end space-x-3 pt-4">
                    <button type="button" id="cancelActivity" class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                        Cancel
                    </button>
                    <button type="submit" id="saveActivity" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700">
                        Save Activity
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Success Modal -->
    <div id="successModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50">
        <div class="bg-white p-6 rounded-lg shadow-xl max-w-md w-full">
            <div class="text-center">
                <svg class="mx-auto h-12 w-12 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <h3 id="successTitle" class="mt-2 text-lg font-medium text-gray-900">Success</h3>
                <p id="successMessage" class="mt-1 text-sm text-gray-500">Operation completed successfully.</p>
                <div class="mt-4">
                    <button type="button" id="closeSuccessModal" class="inline-flex justify-center px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700">
                        OK
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let activities = [];
        let editingActivityId = null;
        let availableRanks = [];

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            loadActivities();
            loadRanks();
            setupEventListeners();
            populateTable();
        });

        // Load activities from localStorage
        function loadActivities() {
            const storedActivities = localStorage.getItem('timesheetActivities');
            if (storedActivities) {
                activities = JSON.parse(storedActivities);
            } else {
                // Initialize with default common activities
                activities = getDefaultActivities();
                saveActivities();
            }
        }

        // Get default common activities
        function getDefaultActivities() {
            return [
                {
                    id: 'act_001',
                    symbol: 'ADM',
                    name: 'Administrative Work',
                    description: 'General administrative tasks and paperwork',
                    category: 'administrative',
                    rank: 'All Ranks',
                    status: 'active',
                    priority: 'normal',
                    createdDate: new Date().toISOString(),
                    createdBy: 'system'
                },
                {
                    id: 'act_002',
                    symbol: 'TRN',
                    name: 'Training',
                    description: 'Professional development and training activities',
                    category: 'training',
                    rank: 'All Ranks',
                    status: 'active',
                    priority: 'high',
                    createdDate: new Date().toISOString(),
                    createdBy: 'system'
                },
                {
                    id: 'act_003',
                    symbol: 'PL',
                    name: 'Paid Leave',
                    description: 'Annual leave, sick leave, and other paid time off',
                    category: 'leave',
                    rank: 'All Ranks',
                    status: 'active',
                    priority: 'normal',
                    createdDate: new Date().toISOString(),
                    createdBy: 'system'
                },
                {
                    id: 'act_004',
                    symbol: 'ACT',
                    name: 'Acting Appointment',
                    description: 'Temporary assignment to higher position duties',
                    category: 'special',
                    rank: 'All Ranks',
                    status: 'active',
                    priority: 'high',
                    createdDate: new Date().toISOString(),
                    createdBy: 'system'
                }
            ];
        }

        // Save activities to localStorage
        function saveActivities() {
            localStorage.setItem('timesheetActivities', JSON.stringify(activities));
        }

        // Load available ranks from localStorage
        function loadRanks() {
            const storedRanks = localStorage.getItem('ranks');
            if (storedRanks) {
                availableRanks = JSON.parse(storedRanks);
            } else {
                // Default ranks if none exist
                availableRanks = [
                    { rankName: 'Senior User', rankIdentifier: 'SU-001' },
                    { rankName: 'Junior User', rankIdentifier: 'JU-001' },
                    { rankName: 'System Admin', rankIdentifier: 'SA-001' },
                    { rankName: 'Section Admin', rankIdentifier: 'SEC-001' },
                    { rankName: 'Division Admin', rankIdentifier: 'DIV-001' },
                    { rankName: 'PR Officer', rankIdentifier: 'PR-001' }
                ];
            }
            populateRankDropdown();
        }

        // Populate rank dropdown in modal
        function populateRankDropdown() {
            const rankSelect = document.getElementById('activityRank');

            // Clear existing options except the first two
            while (rankSelect.options.length > 2) {
                rankSelect.remove(2);
            }

            // Add rank options
            availableRanks.forEach(rank => {
                const option = document.createElement('option');
                option.value = rank.rankName;
                option.textContent = rank.rankName;
                rankSelect.appendChild(option);
            });
        }

        // Setup event listeners
        function setupEventListeners() {
            // Modal controls
            document.getElementById('addActivityBtn').addEventListener('click', openAddActivityModal);
            document.getElementById('closeModal').addEventListener('click', closeActivityModal);
            document.getElementById('cancelActivity').addEventListener('click', closeActivityModal);
            document.getElementById('closeSuccessModal').addEventListener('click', closeSuccessModal);

            // Form submission
            document.getElementById('activityForm').addEventListener('submit', handleActivitySubmit);

            // Symbol validation
            document.getElementById('activitySymbol').addEventListener('input', validateSymbol);
        }

        // Open add activity modal
        function openAddActivityModal() {
            editingActivityId = null;
            document.getElementById('modalTitle').textContent = 'Add New Activity';
            document.getElementById('activityForm').reset();
            document.getElementById('activityId').value = '';
            populateRankDropdown();
            document.getElementById('activityModal').classList.remove('hidden');
        }

        // Open edit activity modal
        function openEditActivityModal(activityId) {
            const activity = activities.find(a => a.id === activityId);
            if (!activity) return;

            editingActivityId = activityId;
            document.getElementById('modalTitle').textContent = 'Edit Activity';

            // Populate form fields
            document.getElementById('activityId').value = activity.id;
            document.getElementById('activitySymbol').value = activity.symbol;
            document.getElementById('activityName').value = activity.name;
            document.getElementById('activityDescription').value = activity.description || '';
            document.getElementById('activityCategory').value = activity.category;
            document.getElementById('activityRank').value = activity.rank || '';
            document.getElementById('activityStatus').value = activity.status;
            document.getElementById('activityPriority').value = activity.priority;

            populateRankDropdown();

            document.getElementById('activityModal').classList.remove('hidden');
        }

        // Close activity modal
        function closeActivityModal() {
            document.getElementById('activityModal').classList.add('hidden');
            editingActivityId = null;
        }

        // Close success modal
        function closeSuccessModal() {
            document.getElementById('successModal').classList.add('hidden');
        }

        // Validate activity symbol
        function validateSymbol() {
            const symbolInput = document.getElementById('activitySymbol');
            const symbol = symbolInput.value.toUpperCase();

            // Convert to uppercase
            symbolInput.value = symbol;

            // Check for duplicates (excluding current activity when editing)
            const existingActivity = activities.find(a =>
                a.symbol === symbol && a.id !== editingActivityId
            );

            if (existingActivity) {
                symbolInput.setCustomValidity('This symbol is already in use');
            } else {
                symbolInput.setCustomValidity('');
            }
        }

        // Handle form submission
        function handleActivitySubmit(event) {
            event.preventDefault();

            const formData = new FormData(event.target);
            const activityData = {};

            // Convert FormData to object
            for (let [key, value] of formData.entries()) {
                activityData[key] = value;
            }

            // Validate symbol uniqueness
            const existingActivity = activities.find(a =>
                a.symbol === activityData.activitySymbol && a.id !== editingActivityId
            );

            if (existingActivity) {
                alert('Activity symbol already exists. Please choose a different symbol.');
                return;
            }

            if (editingActivityId) {
                updateActivity(editingActivityId, activityData);
            } else {
                createActivity(activityData);
            }
        }

        // Create new activity
        function createActivity(activityData) {
            const newActivity = {
                id: 'act_' + Date.now(),
                symbol: activityData.activitySymbol,
                name: activityData.activityName,
                description: activityData.activityDescription || '',
                category: activityData.activityCategory,
                rank: activityData.activityRank,
                status: activityData.activityStatus || 'active',
                priority: activityData.activityPriority || 'normal',
                createdDate: new Date().toISOString(),
                createdBy: 'current_user', // In real app, get from session
                lastModified: new Date().toISOString()
            };

            activities.push(newActivity);
            saveActivities();
            closeActivityModal();
            populateTable();
            showSuccessMessage('Activity Created', 'New activity has been created successfully.');
        }

        // Update existing activity
        function updateActivity(activityId, activityData) {
            const activityIndex = activities.findIndex(a => a.id === activityId);
            if (activityIndex === -1) return;

            const updatedActivity = {
                ...activities[activityIndex],
                symbol: activityData.activitySymbol,
                name: activityData.activityName,
                description: activityData.activityDescription || '',
                category: activityData.activityCategory,
                rank: activityData.activityRank,
                status: activityData.activityStatus || 'active',
                priority: activityData.activityPriority || 'normal',
                lastModified: new Date().toISOString()
            };

            activities[activityIndex] = updatedActivity;
            saveActivities();
            closeActivityModal();
            populateTable();
            showSuccessMessage('Activity Updated', 'Activity has been updated successfully.');
        }

        // Delete activity
        function deleteActivity(activityId) {
            if (!confirm('Are you sure you want to delete this activity? This action cannot be undone.')) {
                return;
            }

            const activityIndex = activities.findIndex(a => a.id === activityId);
            if (activityIndex === -1) return;

            activities.splice(activityIndex, 1);
            saveActivities();
            populateTable();
            showSuccessMessage('Activity Deleted', 'Activity has been deleted successfully.');
        }

        // Show success message
        function showSuccessMessage(title, message) {
            document.getElementById('successTitle').textContent = title;
            document.getElementById('successMessage').textContent = message;
            document.getElementById('successModal').classList.remove('hidden');
        }

        // Populate activities table
        function populateTable() {
            const tableBody = document.getElementById('activitiesTableBody');

            // Clear existing rows
            tableBody.innerHTML = '';

            if (activities.length === 0) {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td colspan="6" class="px-6 py-4 text-center text-sm text-gray-500">
                        No activities found.
                    </td>
                `;
                tableBody.appendChild(row);
            } else {
                activities.forEach(activity => {
                    const row = document.createElement('tr');
                    row.className = 'hover:bg-gray-50';

                    // Format category
                    const categoryColors = {
                        'operational': 'bg-blue-100 text-blue-800',
                        'administrative': 'bg-gray-100 text-gray-800',
                        'training': 'bg-green-100 text-green-800',
                        'leave': 'bg-yellow-100 text-yellow-800',
                        'special': 'bg-purple-100 text-purple-800',
                        'project': 'bg-indigo-100 text-indigo-800',
                        'meeting': 'bg-orange-100 text-orange-800'
                    };

                    const categoryClass = categoryColors[activity.category] || 'bg-gray-100 text-gray-800';
                    const categoryText = getCategoryDisplayName(activity.category);

                    // Format status
                    const statusClass = activity.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
                    const statusText = activity.status.charAt(0).toUpperCase() + activity.status.slice(1);

                    // Format rank
                    const rankClass = activity.rank === 'All Ranks' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800';

                    row.innerHTML = `
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-mono font-bold text-gray-900">${activity.symbol}</div>
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm font-medium text-gray-900">${activity.name}</div>
                            ${activity.description ? `<div class="text-sm text-gray-500 truncate max-w-xs">${activity.description}</div>` : ''}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${categoryClass}">
                                ${categoryText}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${rankClass}">
                                ${activity.rank}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${statusClass}">
                                ${statusText}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                            <button onclick="openEditActivityModal('${activity.id}')" class="text-indigo-600 hover:text-indigo-900">
                                Edit
                            </button>
                            <button onclick="deleteActivity('${activity.id}')" class="text-red-600 hover:text-red-900">
                                Delete
                            </button>
                        </td>
                    `;

                    tableBody.appendChild(row);
                });
            }

            // Update activity count
            document.getElementById('activityCount').textContent = activities.length;
        }

        // Utility functions for category and status formatting
        function getCategoryDisplayName(category) {
            const categoryNames = {
                'operational': 'Operational',
                'administrative': 'Administrative',
                'training': 'Training & Development',
                'leave': 'Leave & Absence',
                'special': 'Special Assignment'
            };
            return categoryNames[category] || category;
        }

        function getTypeDisplayName(type) {
            return type === 'common' ? 'Common Activity' : 'Rank-Specific Activity';
        }

        // Export activities (for future use)
        function exportActivities() {
            const dataStr = JSON.stringify(activities, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = 'timesheet_activities.json';
            link.click();
            URL.revokeObjectURL(url);
        }

        // Import activities (for future use)
        function importActivities(event) {
            const file = event.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const importedActivities = JSON.parse(e.target.result);
                    if (Array.isArray(importedActivities)) {
                        activities = importedActivities;
                        saveActivities();
                        populateTable();
                        showSuccessMessage('Import Successful', 'Activities have been imported successfully.');
                    } else {
                        alert('Invalid file format. Please select a valid JSON file.');
                    }
                } catch (error) {
                    alert('Error reading file. Please ensure it is a valid JSON file.');
                }
            };
            reader.readAsText(file);
        }
    </script>
</body>
</html>

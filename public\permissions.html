<script type="text/javascript">
        var gk_isXlsx = false;
        var gk_xlsxFileLookup = {};
        var gk_fileData = {};
        function filledCell(cell) {
          return cell !== '' && cell != null;
        }
        function loadFileData(filename) {
        if (gk_isXlsx && gk_xlsxFileLookup[filename]) {
            try {
                var workbook = XLSX.read(gk_fileData[filename], { type: 'base64' });
                var firstSheetName = workbook.SheetNames[0];
                var worksheet = workbook.Sheets[firstSheetName];

                // Convert sheet to JSON to filter blank rows
                var jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1, blankrows: false, defval: '' });
                // Filter out blank rows (rows where all cells are empty, null, or undefined)
                var filteredData = jsonData.filter(row => row.some(filledCell));

                // Heuristic to find the header row by ignoring rows with fewer filled cells than the next row
                var headerRowIndex = filteredData.findIndex((row, index) =>
                  row.filter(filledCell).length >= filteredData[index + 1]?.filter(filledCell).length
                );
                // Fallback
                if (headerRowIndex === -1 || headerRowIndex > 25) {
                  headerRowIndex = 0;
                }

                // Convert filtered JSON back to CSV
                var csv = XLSX.utils.aoa_to_sheet(filteredData.slice(headerRowIndex)); // Create a new sheet from filtered array of arrays
                csv = XLSX.utils.sheet_to_csv(csv, { header: 1 });
                return csv;
            } catch (e) {
                console.error(e);
                return "";
            }
        }
        return gk_fileData[filename] || "";
        }
        </script><!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Permissions - OFCA Portal</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="css/ofca-styles.css">
    <script src="js/header.js"></script>
    <script src="js/footer.js"></script>
</head>
<body class="bg-gray-100 min-h-screen p-8">
    <div class="bg-white p-8 rounded-lg shadow-lg w-full max-w-5xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold" id="pageTitle">Manage Rank Permissions</h1>
            <div>
                <a href="index.html" class="text-indigo-600 hover:text-indigo-900 mr-4">Back to User List</a>
                <a id="backToUserDetails" href="#" class="text-indigo-600 hover:text-indigo-900 hidden">Back to User Details</a>
            </div>
        </div>

        <!-- User Information Section (Only visible when viewing a specific user's permissions) -->
        <div id="userInfoSection" class="mb-6 hidden">
            <h2 class="text-lg font-medium text-gray-900 border-b pb-2">User Information</h2>
            <div class="mt-4 grid grid-cols-1 gap-y-2 gap-x-4 sm:grid-cols-2">
                <div>
                    <span class="text-sm font-medium text-gray-500">Name:</span>
                    <span id="userName" class="ml-2 text-sm text-gray-900"></span>
                </div>
                <div>
                    <span class="text-sm font-medium text-gray-500">Username:</span>
                    <span id="userUsername" class="ml-2 text-sm text-gray-900"></span>
                </div>
                <div>
                    <span class="text-sm font-medium text-gray-500">Role:</span>
                    <span id="userRole" class="ml-2 text-sm text-gray-900"></span>
                </div>
                <div>
                    <span class="text-sm font-medium text-gray-500">Rank:</span>
                    <span id="userRank" class="ml-2 text-sm text-gray-900"></span>
                </div>
            </div>
        </div>

        <!-- User Permissions Section (Only visible when viewing a specific user's permissions) -->
        <div id="userPermissionsSection" class="mb-6 hidden">
            <h2 class="text-lg font-medium text-gray-900 border-b pb-2">Your Permissions</h2>
            <div class="mt-4">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Function</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Access</th>
                            </tr>
                        </thead>
                        <tbody id="userPermissionsTable" class="bg-white divide-y divide-gray-200">
                            <!-- Permissions will be dynamically populated here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Permissions Matrix (For PR Officers) -->
        <div id="permissionsMatrixSection">
            <h2 class="text-lg font-medium text-gray-900 border-b pb-2">Rank Permissions Matrix</h2>
            <div class="overflow-x-auto mt-4">
                <table class="min-w-full divide-y divide-gray-200" id="permissionsTable">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rank Identifier</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rank Name</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Create User</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">View User</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Edit User</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Delete User</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200" id="permissionsTableBody">
                        <!-- Rows will be populated by JavaScript -->
                    </tbody>
                </table>
            </div>

            <!-- Save Button (Only visible for PR Officers) -->
            <div id="saveButtonSection" class="mt-6 flex justify-end">
                <button id="savePermissions" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Save Permissions
                </button>
            </div>

            <!-- Success Message -->
            <p id="successMessage" class="mt-4 text-sm text-green-600 text-center hidden">
                Permissions updated successfully.
            </p>
        </div>
    </div>

    <script>
        // Get URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const username = urlParams.get('user');

        // Default ranks and permissions matrix
        const defaultRanks = [
            { rankIdentifier: "R001", rankName: "Senior Officer", permissions: { createUser: true, viewUser: true, editUser: true, deleteUser: false } },
            { rankIdentifier: "R002", rankName: "Junior Officer", permissions: { createUser: false, viewUser: true, editUser: false, deleteUser: false } },
            { rankIdentifier: "R003", rankName: "Manager", permissions: { createUser: true, viewUser: true, editUser: true, deleteUser: true } }
        ];

        // Load ranks from localStorage or use defaults
        let ranks = JSON.parse(localStorage.getItem('ranks')) || defaultRanks;

        // Save ranks to localStorage
        function saveRanks() {
            localStorage.setItem('ranks', JSON.stringify(ranks));
        }

        // Get current user from URL parameter
        function getCurrentUser() {
            if (!username) return null;

            const users = JSON.parse(localStorage.getItem('users')) || [];
            return users.find(user => user.username === username) || null;
        }

        // Get rank details by identifier
        function getRankByIdentifier(identifier) {
            if (!identifier) return null;
            return ranks.find(rank => rank.identifier === identifier || rank.rankIdentifier === identifier) || null;
        }

        // Display user information
        function displayUserInfo(user) {
            if (!user) return;

            // Show user info section
            document.getElementById('userInfoSection').classList.remove('hidden');
            document.getElementById('userPermissionsSection').classList.remove('hidden');

            // Update page title
            document.getElementById('pageTitle').textContent = 'User Permissions';

            // Set back link
            document.getElementById('backToUserDetails').href = `details.html?user=${user.username}`;
            document.getElementById('backToUserDetails').classList.remove('hidden');

            // Populate user info
            document.getElementById('userName').textContent = `${user.firstName} ${user.lastName}`;
            document.getElementById('userUsername').textContent = user.username;
            document.getElementById('userRole').textContent = user.rankName || 'Not assigned';
            document.getElementById('userRank').textContent = user.rankIdentifier || 'Not assigned';

            // If not a PR Officer (based on rank name), hide the permissions matrix
            if (!user.rankName || !user.rankName.includes('PR Officer')) {
                document.getElementById('permissionsMatrixSection').classList.add('hidden');
            }
        }

        // Display user permissions based on their rank
        function displayUserPermissions(user) {
            const permissionsTable = document.getElementById('userPermissionsTable');
            permissionsTable.innerHTML = '';

            // Find user's rank
            const userRank = ranks.find(rank => rank.rankIdentifier === user.rankIdentifier);

            // Define permission types to display
            const permissionTypes = [
                { key: 'createUser', label: 'Create User' },
                { key: 'viewUser', label: 'View User' },
                { key: 'editUser', label: 'Edit User' },
                { key: 'deleteUser', label: 'Delete User' }
            ];

            // Create table rows for each permission
            permissionTypes.forEach(permission => {
                const row = document.createElement('tr');

                const functionCell = document.createElement('td');
                functionCell.className = 'px-6 py-4 whitespace-nowrap text-sm text-gray-500';
                functionCell.textContent = permission.label;

                const accessCell = document.createElement('td');
                accessCell.className = 'px-6 py-4 whitespace-nowrap text-sm';

                // Check if user has this permission
                const hasPermission = userRank && userRank.permissions && userRank.permissions[permission.key];

                if (hasPermission) {
                    accessCell.innerHTML = '<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Allowed</span>';
                } else {
                    accessCell.innerHTML = '<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Denied</span>';
                }

                row.appendChild(functionCell);
                row.appendChild(accessCell);
                permissionsTable.appendChild(row);
            });
        }

        // Populate permissions table
        function populatePermissionsTable() {
            const tableBody = document.getElementById('permissionsTableBody');
            tableBody.innerHTML = '';

            ranks.forEach(rank => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${rank.rankIdentifier}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${rank.rankName || ''}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <input type="checkbox" class="createUser" data-rank="${rank.rankIdentifier}" ${rank.permissions && rank.permissions.createUser ? 'checked' : ''}>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <input type="checkbox" class="viewUser" data-rank="${rank.rankIdentifier}" ${rank.permissions && rank.permissions.viewUser ? 'checked' : ''}>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <input type="checkbox" class="editUser" data-rank="${rank.rankIdentifier}" ${rank.permissions && rank.permissions.editUser ? 'checked' : ''}>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <input type="checkbox" class="deleteUser" data-rank="${rank.rankIdentifier}" ${rank.permissions && rank.permissions.deleteUser ? 'checked' : ''}>
                    </td>
                `;
                tableBody.appendChild(row);
            });
        }

        // Save permissions on button click
        document.getElementById('savePermissions').addEventListener('click', () => {
            ranks.forEach(rank => {
                rank.permissions = {
                    createUser: document.querySelector(`.createUser[data-rank="${rank.rankIdentifier}"]`).checked,
                    viewUser: document.querySelector(`.viewUser[data-rank="${rank.rankIdentifier}"]`).checked,
                    editUser: document.querySelector(`.editUser[data-rank="${rank.rankIdentifier}"]`).checked,
                    deleteUser: document.querySelector(`.deleteUser[data-rank="${rank.rankIdentifier}"]`).checked
                };
            });

            saveRanks();

            // Show success message
            const successMessage = document.getElementById('successMessage');
            successMessage.classList.remove('hidden');
            setTimeout(() => successMessage.classList.add('hidden'), 3000);
        });

        // Function to check user permissions (for simulation)
        function checkUserPermission(username, permission) {
            const users = JSON.parse(localStorage.getItem('users')) || [];
            const user = users.find(u => u.username === username);
            if (!user) return false;

            const rank = ranks.find(r => r.rankIdentifier === user.rankIdentifier);
            if (!rank) return false;

            return rank.permissions[permission] || false;
        }

        // Initialize the page
        document.addEventListener('DOMContentLoaded', function() {
            // Update ranks from users in localStorage (to ensure all rankIdentifiers are included)
            const users = JSON.parse(localStorage.getItem('users')) || [];
            users.forEach(user => {
                if (user.rankIdentifier && !ranks.some(r => r.rankIdentifier === user.rankIdentifier)) {
                    ranks.push({
                        rankIdentifier: user.rankIdentifier,
                        rankName: user.rankName || 'Unknown Rank',
                        permissions: { createUser: false, viewUser: false, editUser: false, deleteUser: false }
                    });
                }
            });
            saveRanks();

            // Populate the permissions table
            populatePermissionsTable();

            // If a username is provided, show that user's permissions
            const currentUser = getCurrentUser();
            if (currentUser) {
                displayUserInfo(currentUser);
                displayUserPermissions(currentUser);

                // If not a PR Officer (based on rank name), hide the save button
                if (!currentUser.rankName || !currentUser.rankName.includes('PR Officer')) {
                    document.getElementById('saveButtonSection').classList.add('hidden');
                }
            }
        });
    </script>
</body>
</html>
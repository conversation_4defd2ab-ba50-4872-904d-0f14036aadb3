<script type="text/javascript">
        var gk_isXlsx = false;
        var gk_xlsxFileLookup = {};
        var gk_fileData = {};
        function filledCell(cell) {
          return cell !== '' && cell != null;
        }
        function loadFileData(filename) {
        if (gk_isXlsx && gk_xlsxFileLookup[filename]) {
            try {
                var workbook = XLSX.read(gk_fileData[filename], { type: 'base64' });
                var firstSheetName = workbook.SheetNames[0];
                var worksheet = workbook.Sheets[firstSheetName];

                // Convert sheet to JSON to filter blank rows
                var jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1, blankrows: false, defval: '' });
                // Filter out blank rows (rows where all cells are empty, null, or undefined)
                var filteredData = jsonData.filter(row => row.some(filledCell));

                // Heuristic to find the header row by ignoring rows with fewer filled cells than the next row
                var headerRowIndex = filteredData.findIndex((row, index) =>
                  row.filter(filledCell).length >= filteredData[index + 1]?.filter(filledCell).length
                );
                // Fallback
                if (headerRowIndex === -1 || headerRowIndex > 25) {
                  headerRowIndex = 0;
                }

                // Convert filtered JSON back to CSV
                var csv = XLSX.utils.aoa_to_sheet(filteredData.slice(headerRowIndex)); // Create a new sheet from filtered array of arrays
                csv = XLSX.utils.sheet_to_csv(csv, { header: 1 });
                return csv;
            } catch (e) {
                console.error(e);
                return "";
            }
        }
        return gk_fileData[filename] || "";
        }
        </script><!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Rank Permissions - OFCA Portal</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 min-h-screen p-8">
    <div class="bg-white p-8 rounded-lg shadow-lg w-full max-w-5xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold">Manage Rank Permissions</h1>
            <a href="index.html" class="text-indigo-600 hover:text-indigo-900">Back to User List</a>
        </div>

        <!-- Permissions Matrix -->
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200" id="permissionsTable">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rank Identifier</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rank Name</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Create User</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">View User</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Edit User</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Delete User</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200" id="permissionsTableBody">
                    <!-- Rows will be populated by JavaScript -->
                </tbody>
            </table>
        </div>

        <!-- Save Button -->
        <div class="mt-6 flex justify-end">
            <button id="savePermissions" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                Save Permissions
            </button>
        </div>

        <!-- Success Message -->
        <p id="successMessage" class="mt-4 text-sm text-green-600 text-center hidden">
            Permissions updated successfully.
        </p>
    </div>

    <script>
        // Default ranks and permissions matrix
        const defaultRanks = [
            { rankIdentifier: "R001", rankName: "Senior Officer", permissions: { createUser: true, viewUser: true, editUser: true, deleteUser: false } },
            { rankIdentifier: "R002", rankName: "Junior Officer", permissions: { createUser: false, viewUser: true, editUser: false, deleteUser: false } },
            { rankIdentifier: "R003", rankName: "Manager", permissions: { createUser: true, viewUser: true, editUser: true, deleteUser: true } }
        ];

        // Load ranks from localStorage or use defaults
        let ranks = JSON.parse(localStorage.getItem('ranks')) || defaultRanks;

        // Save ranks to localStorage
        function saveRanks() {
            localStorage.setItem('ranks', JSON.stringify(ranks));
        }

        // Populate permissions table
        function populatePermissionsTable() {
            const tableBody = document.getElementById('permissionsTableBody');
            tableBody.innerHTML = '';

            ranks.forEach(rank => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${rank.rankIdentifier}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${rank.rankName}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <input type="checkbox" class="createUser" data-rank="${rank.rankIdentifier}" ${rank.permissions.createUser ? 'checked' : ''}>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <input type="checkbox" class="viewUser" data-rank="${rank.rankIdentifier}" ${rank.permissions.viewUser ? 'checked' : ''}>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <input type="checkbox" class="editUser" data-rank="${rank.rankIdentifier}" ${rank.permissions.editUser ? 'checked' : ''}>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <input type="checkbox" class="deleteUser" data-rank="${rank.rankIdentifier}" ${rank.permissions.deleteUser ? 'checked' : ''}>
                    </td>
                `;
                tableBody.appendChild(row);
            });
        }

        // Save permissions on button click
        document.getElementById('savePermissions').addEventListener('click', () => {
            ranks.forEach(rank => {
                rank.permissions = {
                    createUser: document.querySelector(`.createUser[data-rank="${rank.rankIdentifier}"]`).checked,
                    viewUser: document.querySelector(`.viewUser[data-rank="${rank.rankIdentifier}"]`).checked,
                    editUser: document.querySelector(`.editUser[data-rank="${rank.rankIdentifier}"]`).checked,
                    deleteUser: document.querySelector(`.deleteUser[data-rank="${rank.rankIdentifier}"]`).checked
                };
            });

            saveRanks();

            // Show success message
            const successMessage = document.getElementById('successMessage');
            successMessage.classList.remove('hidden');
            setTimeout(() => successMessage.classList.add('hidden'), 3000);
        });

        // Function to check user permissions (for simulation)
        function checkUserPermission(username, permission) {
            const users = JSON.parse(localStorage.getItem('users')) || [];
            const user = users.find(u => u.username === username);
            if (!user) return false;

            const rank = ranks.find(r => r.rankIdentifier === user.rankIdentifier);
            if (!rank) return false;

            return rank.permissions[permission] || false;
        }

        // Example usage of permission check (for demonstration)
        console.log('Permission check example:', checkUserPermission('jdoe', 'createUser'));

        // Initial table population
        populatePermissionsTable();

        // Update ranks from users in localStorage (to ensure all rankIdentifiers are included)
        const users = JSON.parse(localStorage.getItem('users')) || [];
        users.forEach(user => {
            if (user.rankIdentifier && !ranks.some(r => r.rankIdentifier === user.rankIdentifier)) {
                ranks.push({
                    rankIdentifier: user.rankIdentifier,
                    rankName: user.rankName || 'Unknown Rank',
                    permissions: { createUser: false, viewUser: false, editUser: false, deleteUser: false }
                });
            }
        });
        saveRanks();
        populatePermissionsTable();
    </script>
</body>
</html>
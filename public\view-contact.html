<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>View Contact Details - OFCA Portal</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="css/ofca-styles.css">
    <script src="js/header.js"></script>
    <script src="js/footer.js"></script>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="p-8">
    <div class="bg-white p-8 rounded-lg shadow-lg w-full max-w-4xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold">Contact Details</h1>
            <a href="index.html" class="text-indigo-600 hover:text-indigo-900">Back to User List</a>
        </div>

        <div class="space-y-6">
            <!-- Personal Information Section -->
            <div>
                <h2 class="text-lg font-medium text-gray-900 border-b pb-2">Personal Information</h2>
                <div class="mt-4 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                    <div class="sm:col-span-6">
                        <p class="block text-sm font-medium text-gray-700">Full Name</p>
                        <p class="mt-1 block w-full px-3 py-2 border border-gray-100 rounded-md shadow-sm bg-gray-50 sm:text-sm" id="fullName">Loading...</p>
                    </div>

                    <div class="sm:col-span-3">
                        <p class="block text-sm font-medium text-gray-700">Position</p>
                        <p class="mt-1 block w-full px-3 py-2 border border-gray-100 rounded-md shadow-sm bg-gray-50 sm:text-sm" id="position">Loading...</p>
                    </div>

                    <div class="sm:col-span-3">
                        <p class="block text-sm font-medium text-gray-700">Section</p>
                        <p class="mt-1 block w-full px-3 py-2 border border-gray-100 rounded-md shadow-sm bg-gray-50 sm:text-sm" id="section">Loading...</p>
                    </div>

                    <div class="sm:col-span-3">
                        <p class="block text-sm font-medium text-gray-700">Division</p>
                        <p class="mt-1 block w-full px-3 py-2 border border-gray-100 rounded-md shadow-sm bg-gray-50 sm:text-sm" id="division">Loading...</p>
                    </div>

                    <div class="sm:col-span-3">
                        <p class="block text-sm font-medium text-gray-700">Branch</p>
                        <p class="mt-1 block w-full px-3 py-2 border border-gray-100 rounded-md shadow-sm bg-gray-50 sm:text-sm" id="branch">Loading...</p>
                    </div>
                </div>
            </div>

            <!-- Contact Information Section -->
            <div>
                <h2 class="text-lg font-medium text-gray-900 border-b pb-2">Contact Information</h2>
                <div class="mt-4 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                    <div class="sm:col-span-3">
                        <p class="block text-sm font-medium text-gray-700">Office Telephone</p>
                        <p class="mt-1 block w-full px-3 py-2 border border-gray-100 rounded-md shadow-sm bg-gray-50 sm:text-sm" id="officeTelephone">Loading...</p>
                    </div>

                    <div class="sm:col-span-3">
                        <p class="block text-sm font-medium text-gray-700">Fax</p>
                        <p class="mt-1 block w-full px-3 py-2 border border-gray-100 rounded-md shadow-sm bg-gray-50 sm:text-sm" id="fax">Loading...</p>
                    </div>

                    <div class="sm:col-span-3">
                        <p class="block text-sm font-medium text-gray-700">Other Telephone (Mobile)</p>
                        <p class="mt-1 block w-full px-3 py-2 border border-gray-100 rounded-md shadow-sm bg-gray-50 sm:text-sm" id="mobile">Loading...</p>
                    </div>

                    <div class="sm:col-span-3">
                        <p class="block text-sm font-medium text-gray-700">Email</p>
                        <p class="mt-1 block w-full px-3 py-2 border border-gray-100 rounded-md shadow-sm bg-gray-50 sm:text-sm" id="email">Loading...</p>
                    </div>

                    <div class="sm:col-span-6">
                        <p class="block text-sm font-medium text-gray-700">Office Address</p>
                        <p class="mt-1 block w-full px-3 py-2 border border-gray-100 rounded-md shadow-sm bg-gray-50 sm:text-sm min-h-[72px]" id="officeAddress">Loading...</p>
                    </div>
                </div>
            </div>

            <div class="flex justify-end space-x-3">
                <a href="index.html" class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Back to List
                </a>
            </div>
        </div>
    </div>

    <script>
        // Get users from localStorage
        const users = JSON.parse(localStorage.getItem('users')) || [];

        // Get user from query parameter
        const urlParams = new URLSearchParams(window.location.search);
        const username = urlParams.get('user');
        const user = users.find(u => u.username === username);

        // Function to display contact data
        function displayContactData(userData) {
            // Personal Information
            document.getElementById('fullName').textContent = `${userData.firstName || ''} ${userData.lastName || ''}`.trim() || 'Not provided';
            document.getElementById('position').textContent = userData.position || 'Not provided';
            document.getElementById('section').textContent = userData.section || 'Not provided';
            document.getElementById('division').textContent = userData.division || 'Not provided';
            document.getElementById('branch').textContent = userData.branch || 'Not provided';

            // Contact Information
            document.getElementById('officeTelephone').textContent = userData.officeTelephone || 'Not provided';
            document.getElementById('fax').textContent = userData.fax || 'Not provided';
            document.getElementById('mobile').textContent = userData.mobile || userData.phone || 'Not provided';
            document.getElementById('email').textContent = userData.email || 'Not provided';
            document.getElementById('officeAddress').textContent = userData.officeAddress || 'Not provided';
        }

        // Display user details or show error
        if (user) {
            displayContactData(user);
        } else {
            // Show error message if user not found
            const sections = document.querySelectorAll('.space-y-6 > div');
            sections.forEach(section => {
                const grid = section.querySelector('.grid');
                if (grid) {
                    grid.innerHTML = '<p class="col-span-6 text-red-600 py-4">User not found.</p>';
                }
            });
        }
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Permissions - OFCA Portal</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="css/ofca-styles.css">
    <script src="js/header.js"></script>
    <script src="js/footer.js"></script>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="p-8">
        <div class="bg-white p-8 rounded-lg shadow-lg w-full max-w-4xl mx-auto">
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-2xl font-bold">My Permissions</h1>
                <div>
                    <a href="index.html" class="text-indigo-600 hover:text-indigo-900 mr-4">Back to User List</a>
                    <a id="rankManagementLink" href="permissions.html" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 hidden">Manage Rank Permissions</a>
                </div>
            </div>

            <!-- User Information Section -->
            <div class="mb-6">
                <h2 class="text-lg font-medium text-gray-900 border-b pb-2">User Information</h2>
                <div class="mt-4 grid grid-cols-1 gap-y-2 gap-x-4 sm:grid-cols-2">
                    <div>
                        <span class="text-sm font-medium text-gray-500">Name:</span>
                        <span id="userName" class="ml-2 text-sm text-gray-900"></span>
                    </div>
                    <div>
                        <span class="text-sm font-medium text-gray-500">Username:</span>
                        <span id="userUsername" class="ml-2 text-sm text-gray-900"></span>
                    </div>
                    <div>
                        <span class="text-sm font-medium text-gray-500">Role:</span>
                        <span id="userRole" class="ml-2 text-sm text-gray-900"></span>
                    </div>
                    <div>
                        <span class="text-sm font-medium text-gray-500">Rank:</span>
                        <span id="userRank" class="ml-2 text-sm text-gray-900"></span>
                    </div>
                </div>
            </div>

            <!-- Current User Permissions Section -->
            <div class="mb-6">
                <h2 class="text-lg font-medium text-gray-900 border-b pb-2">Your Permissions</h2>
                <div class="mt-4">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Function</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Access</th>
                                </tr>
                            </thead>
                            <tbody id="userPermissionsTable" class="bg-white divide-y divide-gray-200">
                                <!-- Permissions will be dynamically populated here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Rank-based Permissions Matrix (Only visible to PR Officers) -->
            <div id="rankPermissionsMatrix" class="hidden">
                <h2 class="text-lg font-medium text-gray-900 border-b pb-2">All Ranks Permissions Matrix</h2>
                <p class="mt-2 text-sm text-gray-500">This matrix shows all permissions assigned to each rank in the system.</p>
                
                <div class="mt-4">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rank</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Create User</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">View User</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Edit User</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Delete User</th>
                                </tr>
                            </thead>
                            <tbody id="rankPermissionsTable" class="bg-white divide-y divide-gray-200">
                                <!-- Rank permissions will be dynamically populated here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Get current user from localStorage or URL parameter
        function getCurrentUser() {
            // Check if username is provided in URL
            const urlParams = new URLSearchParams(window.location.search);
            const usernameParam = urlParams.get('user');
            
            // Get all users
            const users = JSON.parse(localStorage.getItem('users')) || [];
            
            // If username is provided in URL, find that user
            if (usernameParam) {
                return users.find(user => user.username === usernameParam) || null;
            }
            
            // Otherwise, use the first user as a demo (in a real app, this would be the logged-in user)
            return users.length > 0 ? users[0] : null;
        }

        // Display user information
        function displayUserInfo(user) {
            if (!user) return;
            
            document.getElementById('userName').textContent = `${user.firstName} ${user.lastName}`;
            document.getElementById('userUsername').textContent = user.username;
            document.getElementById('userRole').textContent = user.role || 'Not assigned';
            document.getElementById('userRank').textContent = user.rankIdentifier || 'Not assigned';
            
            // Show rank management link for PR Officers
            if (user.role === 'PR Officer') {
                document.getElementById('rankManagementLink').classList.remove('hidden');
                document.getElementById('rankPermissionsMatrix').classList.remove('hidden');
            }
        }

        // Display user permissions based on their rank
        function displayUserPermissions(user) {
            const permissionsTable = document.getElementById('userPermissionsTable');
            permissionsTable.innerHTML = '';
            
            // Get ranks from localStorage
            const ranks = JSON.parse(localStorage.getItem('ranks')) || [];
            
            // Find user's rank
            const userRank = ranks.find(rank => rank.rankIdentifier === user.rankIdentifier);
            
            // Define permission types to display
            const permissionTypes = [
                { key: 'createUser', label: 'Create User' },
                { key: 'viewUser', label: 'View User' },
                { key: 'editUser', label: 'Edit User' },
                { key: 'deleteUser', label: 'Delete User' }
            ];
            
            // Create table rows for each permission
            permissionTypes.forEach(permission => {
                const row = document.createElement('tr');
                
                const functionCell = document.createElement('td');
                functionCell.className = 'px-6 py-4 whitespace-nowrap text-sm text-gray-500';
                functionCell.textContent = permission.label;
                
                const accessCell = document.createElement('td');
                accessCell.className = 'px-6 py-4 whitespace-nowrap text-sm';
                
                // Check if user has this permission
                const hasPermission = userRank && userRank.permissions && userRank.permissions[permission.key];
                
                if (hasPermission) {
                    accessCell.innerHTML = '<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Allowed</span>';
                } else {
                    accessCell.innerHTML = '<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Denied</span>';
                }
                
                row.appendChild(functionCell);
                row.appendChild(accessCell);
                permissionsTable.appendChild(row);
            });
        }

        // Display all ranks and their permissions (for PR Officers)
        function displayRankPermissionsMatrix() {
            const rankPermissionsTable = document.getElementById('rankPermissionsTable');
            rankPermissionsTable.innerHTML = '';
            
            // Get all ranks
            const ranks = JSON.parse(localStorage.getItem('ranks')) || [];
            
            // Create table rows for each rank
            ranks.forEach(rank => {
                const row = document.createElement('tr');
                
                const rankCell = document.createElement('td');
                rankCell.className = 'px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900';
                rankCell.textContent = `${rank.rankName} (${rank.rankIdentifier})`;
                row.appendChild(rankCell);
                
                // Add cells for each permission type
                const permissionTypes = ['createUser', 'viewUser', 'editUser', 'deleteUser'];
                permissionTypes.forEach(permType => {
                    const permCell = document.createElement('td');
                    permCell.className = 'px-6 py-4 whitespace-nowrap text-sm text-center';
                    
                    const hasPermission = rank.permissions && rank.permissions[permType];
                    if (hasPermission) {
                        permCell.innerHTML = '<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">✓</span>';
                    } else {
                        permCell.innerHTML = '<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">✗</span>';
                    }
                    
                    row.appendChild(permCell);
                });
                
                rankPermissionsTable.appendChild(row);
            });
        }

        // Initialize the page
        document.addEventListener('DOMContentLoaded', function() {
            const currentUser = getCurrentUser();
            
            if (currentUser) {
                displayUserInfo(currentUser);
                displayUserPermissions(currentUser);
                
                // If user is a PR Officer, show the rank permissions matrix
                if (currentUser.role === 'PR Officer') {
                    displayRankPermissionsMatrix();
                }
            } else {
                // Handle case where no user is found
                alert('No user information found. Please log in again.');
                window.location.href = 'index.html';
            }
        });
    </script>
</body>
</html>

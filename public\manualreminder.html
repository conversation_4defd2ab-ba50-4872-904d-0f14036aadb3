<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Timesheet Reminder System</title>
  <script src="https://cdn.jsdelivr.net/npm/react@18.2.0/umd/react.production.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/react-dom@18.2.0/umd/react-dom.production.min.js"></script>
  <script src="https://cdn.tailwindcss.com"></script>
  <script src="https://cdn.jsdelivr.net/npm/@babel/standalone@7.22.9/Babel.min.js"></script>
</head>
<body>
  <div id="root" class="min-h-screen bg-gray-100"></div>

  <script type="text/babel">
    const { useState } = React;

    // Sample data for demonstration
    const initialTimesheets = [
      { id: 1, user: "<PERSON>", timesheet: "TS001", status: "Pending", lastReminder: "2025-05-20" },
      { id: 2, user: "<PERSON> <PERSON>", timesheet: "TS002", status: "Pending", lastReminder: "2025-05-18" },
      { id: 3, user: "Alice Johnson", timesheet: "TS003", status: "Submitted", lastReminder: "2025-05-15" },
    ];

    const initialHistory = [
      { id: 1, user: "John Doe", timesheet: "TS001", message: "Please submit your timesheet for TS001.", sentAt: "2025-05-20 10:00" },
      { id: 2, user: "Jane Smith", timesheet: "TS002", message: "Reminder: TS002 is due.", sentAt: "2025-05-18 09:30" },
    ];

    // Main App Component
    const App = () => {
      const [timesheets, setTimesheets] = useState(initialTimesheets);
      const [selectedTimesheets, setSelectedTimesheets] = useState([]);
      const [reminderMessage, setReminderMessage] = useState("Please submit your timesheet by the deadline.");
      const [history, setHistory] = useState(initialHistory);
      const [showHistory, setShowHistory] = useState(false);
      const [selectedUser, setSelectedUser] = useState(null);

      // Handle timesheet selection
      const handleSelectTimesheet = (id) => {
        setSelectedTimesheets((prev) =>
          prev.includes(id) ? prev.filter((item) => item !== id) : [...prev, id]
        );
      };

      // Handle sending reminders
      const handleSendReminder = () => {
        if (selectedTimesheets.length === 0) {
          alert("Please select at least one timesheet.");
          return;
        }
        const newHistory = selectedTimesheets.map((id) => {
          const timesheet = timesheets.find((t) => t.id === id);
          return {
            id: history.length + 1,
            user: timesheet.user,
            timesheet: timesheet.timesheet,
            message: reminderMessage,
            sentAt: new Date().toLocaleString(),
          };
        });
        setHistory([...history, ...newHistory]);
        alert("Reminders sent successfully!");
        setSelectedTimesheets([]);
      };

      // Toggle history view
      const toggleHistory = (user) => {
        setSelectedUser(user);
        setShowHistory(true);
      };

      return (
        <div className="container mx-auto p-4">
          <h1 className="text-2xl font-bold mb-4">Timesheet Reminder System</h1>

          {/* Timesheet Selection Table */}
          <div className="mb-6">
            <h2 className="text-xl font-semibold mb-2">Select Timesheets</h2>
            <table className="w-full border-collapse bg-white shadow-md rounded">
              <thead>
                <tr className="bg-gray-200">
                  <th className="border p-2">Select</th>
                  <th className="border p-2">User</th>
                  <th className="border p-2">Timesheet ID</th>
                  <th className="border p-2">Status</th>
                  <th className="border p-2">Last Reminder</th>
                  <th className="border p-2">History</th>
                </tr>
              </thead>
              <tbody>
                {timesheets.map((timesheet) => (
                  <tr key={timesheet.id}>
                    <td className="border p-2">
                      <input
                        type="checkbox"
                        checked={selectedTimesheets.includes(timesheet.id)}
                        onChange={() => handleSelectTimesheet(timesheet.id)}
                      />
                    </td>
                    <td className="border p-2">{timesheet.user}</td>
                    <td className="border p-2">{timesheet.timesheet}</td>
                    <td className="border p-2">{timesheet.status}</td>
                    <td className="border p-2">{timesheet.lastReminder}</td>
                    <td className="border p-2">
                      <button
                        className="text-blue-500 hover:underline"
                        onClick={() => toggleHistory(timesheet.user)}
                      >
                        View
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Reminder Message Editor */}
          <div className="mb-6">
            <h2 className="text-xl font-semibold mb-2">Edit Reminder Message</h2>
            <textarea
              className="w-full p-2 border rounded"
              rows="4"
              value={reminderMessage}
              onChange={(e) => setReminderMessage(e.target.value)}
              placeholder="Enter reminder message"
            />
            <button
              className="mt-2 bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
              onClick={handleSendReminder}
            >
              Send Reminders
            </button>
          </div>

          {/* Reminder History */}
          {showHistory && (
            <div className="mb-6">
              <h2 className="text-xl font-semibold mb-2">Reminder History {selectedUser && `for ${selectedUser}`}</h2>
              <table className="w-full border-collapse bg-white shadow-md rounded">
                <thead>
                  <tr className="bg-gray-200">
                    <th className="border p-2">User</th>
                    <th className="border p-2">Timesheet ID</th>
                    <th className="border p-2">Message</th>
                    <th className="border p-2">Sent At</th>
                  </tr>
                </thead>
                <tbody>
                  {history
                    .filter((entry) => !selectedUser || entry.user === selectedUser)
                    .map((entry) => (
                      <tr key={entry.id}>
                        <td className="border p-2">{entry.user}</td>
                        <td className="border p-2">{entry.timesheet}</td>
                        <td className="border p-2">{entry.message}</td>
                        <td className="border p-2">{entry.sentAt}</td>
                      </tr>
                    ))}
                </tbody>
              </table>
              <button
                className="mt-2 bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600"
                onClick={() => setShowHistory(false)}
              >
                Close History
              </button>
            </div>
          )}
        </div>
      );
    };

    // Render the app
    const root = ReactDOM.createRoot(document.getElementById('root'));
    root.render(<App />);
  </script>
</body>
</html>

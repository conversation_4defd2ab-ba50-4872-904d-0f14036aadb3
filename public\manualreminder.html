<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manual Timesheet Reminder System - OFCA Portal</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="css/ofca-styles.css">
    <script src="js/header.js"></script>
    <script src="js/footer.js"></script>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="p-8">
        <div class="bg-white p-8 rounded-lg shadow-lg w-full max-w-7xl mx-auto">
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-2xl font-bold">Manual Timesheet Reminder System</h1>
                <a href="index.html" class="text-indigo-600 hover:text-indigo-900">Back to User List</a>
            </div>

            <!-- Filter and Search Section -->
            <div class="mb-6 bg-gray-50 p-4 rounded-lg">
                <h2 class="text-lg font-medium text-gray-900 mb-4">Filter Users and Timesheets</h2>
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                        <label for="userSearch" class="block text-sm font-medium text-gray-700">Search Users</label>
                        <input type="text" id="userSearch" placeholder="Enter name or username..." class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>
                    <div>
                        <label for="statusFilter" class="block text-sm font-medium text-gray-700">Timesheet Status</label>
                        <select id="statusFilter" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                            <option value="all">All Statuses</option>
                            <option value="pending">Pending</option>
                            <option value="overdue">Overdue</option>
                            <option value="submitted">Submitted</option>
                        </select>
                    </div>
                    <div>
                        <label for="departmentFilter" class="block text-sm font-medium text-gray-700">Department</label>
                        <select id="departmentFilter" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                            <option value="all">All Departments</option>
                            <option value="Revenue">Revenue</option>
                            <option value="IT">IT</option>
                            <option value="HR">HR</option>
                            <option value="Finance">Finance</option>
                        </select>
                    </div>
                    <div class="flex items-end">
                        <button id="applyFilters" class="w-full px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            Apply Filters
                        </button>
                    </div>
                </div>
            </div>

            <!-- User and Timesheet Selection Table -->
            <div class="mb-6">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-lg font-medium text-gray-900">Select Users and Timesheets</h2>
                    <div class="flex space-x-2">
                        <button id="selectAllBtn" class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50">Select All</button>
                        <button id="clearSelectionBtn" class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50">Clear Selection</button>
                        <span id="selectionCount" class="px-3 py-1 text-sm text-gray-600">0 selected</span>
                    </div>
                </div>

                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200" id="timesheetTable">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <input type="checkbox" id="selectAllCheckbox" class="rounded">
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Department</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Timesheet ID</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Period</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Due Date</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Reminder</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody id="timesheetTableBody" class="bg-white divide-y divide-gray-200">
                            <!-- Table rows will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Reminder Message Templates -->
            <div class="mb-6">
                <h2 class="text-lg font-medium text-gray-900 mb-4">Reminder Message Templates</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                        <label for="templateSelect" class="block text-sm font-medium text-gray-700">Select Template</label>
                        <select id="templateSelect" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                            <option value="standard">Standard Reminder</option>
                            <option value="urgent">Urgent Reminder</option>
                            <option value="final">Final Notice</option>
                            <option value="custom">Custom Message</option>
                        </select>
                    </div>
                    <div class="flex items-end space-x-2">
                        <button id="loadTemplateBtn" class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">Load Template</button>
                        <button id="saveTemplateBtn" class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">Save as Template</button>
                    </div>
                </div>

                <div>
                    <label for="reminderMessage" class="block text-sm font-medium text-gray-700">Reminder Message</label>
                    <textarea id="reminderMessage" rows="4" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="Enter your reminder message here...">Dear {userName},

This is a reminder that your timesheet for the period {period} (ID: {timesheetId}) is due on {dueDate}.

Please submit your timesheet as soon as possible to avoid any delays in processing.

Thank you,
Revenue Section</textarea>
                </div>

                <div class="mt-4 p-3 bg-blue-50 rounded-md">
                    <p class="text-sm text-blue-700">
                        <strong>Available placeholders:</strong> {userName}, {timesheetId}, {period}, {dueDate}, {department}
                    </p>
                </div>
            </div>

            <!-- Send Reminder Section -->
            <div class="mb-6 bg-yellow-50 p-4 rounded-lg">
                <div class="flex justify-between items-center">
                    <div>
                        <h3 class="text-lg font-medium text-gray-900">Send Reminders</h3>
                        <p class="text-sm text-gray-600" id="reminderSummary">No users selected</p>
                    </div>
                    <button id="sendRemindersBtn" disabled class="px-6 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:bg-gray-400 disabled:cursor-not-allowed">
                        Send Reminders
                    </button>
                </div>
            </div>

            <!-- Reminder History Section -->
            <div class="mb-6">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-lg font-medium text-gray-900">Reminder History</h2>
                    <div class="flex space-x-2">
                        <input type="text" id="historySearch" placeholder="Search history..." class="px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm">
                        <button id="exportHistoryBtn" class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">Export</button>
                    </div>
                </div>

                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date/Time</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Timesheet ID</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Message Preview</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sent By</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody id="historyTableBody" class="bg-white divide-y divide-gray-200">
                            <!-- History rows will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Success Modal -->
    <div id="successModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden">
        <div class="bg-white p-6 rounded-lg shadow-xl max-w-md w-full">
            <div class="text-center">
                <svg class="mx-auto h-12 w-12 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <h3 class="mt-2 text-lg font-medium text-gray-900">Reminders Sent Successfully</h3>
                <p class="mt-1 text-sm text-gray-500" id="successMessage">
                    Reminders have been sent to the selected users.
                </p>
                <div class="mt-4">
                    <button type="button" id="closeSuccessModal" class="inline-flex justify-center px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700">
                        OK
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Message Preview Modal -->
    <div id="messagePreviewModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden">
        <div class="bg-white p-6 rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">Reminder Preview</h3>
                <button type="button" id="closePreviewModal" class="text-gray-400 hover:text-gray-500">
                    <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <!-- Summary Section -->
            <div class="mb-6 p-4 bg-blue-50 rounded-lg">
                <h4 class="font-medium text-blue-900 mb-2">Reminder Summary</h4>
                <p class="text-sm text-blue-700" id="previewSummary"></p>
            </div>

            <!-- Recipients List -->
            <div class="mb-6">
                <h4 class="font-medium text-gray-900 mb-3">Recipients (<span id="recipientCount"></span>)</h4>
                <div class="max-h-40 overflow-y-auto border border-gray-200 rounded-md">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">User</th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Email</th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Timesheet ID</th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
                            </tr>
                        </thead>
                        <tbody id="recipientsList" class="bg-white divide-y divide-gray-200">
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Message Preview Tabs -->
            <div class="mb-4">
                <div class="border-b border-gray-200">
                    <nav class="-mb-px flex space-x-8">
                        <button id="samplePreviewTab" class="border-indigo-500 text-indigo-600 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm" onclick="showPreviewTab('sample')">
                            Sample Message
                        </button>
                        <button id="allPreviewTab" class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm" onclick="showPreviewTab('all')">
                            All Messages
                        </button>
                    </nav>
                </div>
            </div>

            <!-- Sample Message Preview -->
            <div id="samplePreviewContent" class="mb-6">
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700">Sample Message (for first recipient):</label>
                    <div class="mt-2 p-4 border border-gray-300 rounded-md bg-gray-50">
                        <div class="mb-2">
                            <span class="font-medium">To:</span> <span id="sampleRecipient" class="text-gray-700"></span>
                        </div>
                        <div class="mb-2">
                            <span class="font-medium">Subject:</span> <span class="text-gray-700">Timesheet Submission Reminder</span>
                        </div>
                        <div class="border-t pt-2 mt-2">
                            <div id="sampleMessage" class="text-sm whitespace-pre-wrap text-gray-800"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- All Messages Preview -->
            <div id="allPreviewContent" class="mb-6 hidden">
                <div class="max-h-60 overflow-y-auto">
                    <div id="allMessagesContainer" class="space-y-4">
                        <!-- Individual message previews will be populated here -->
                    </div>
                </div>
            </div>

            <div class="flex justify-end space-x-3">
                <button type="button" id="cancelPreview" class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                    Cancel
                </button>
                <button type="button" id="confirmSend" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700">
                    <span id="confirmSendText">Send Reminders</span>
                </button>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let users = [];
        let timesheets = [];
        let selectedTimesheets = [];
        let reminderHistory = [];
        let messageTemplates = {};

        // Initialize the page
        document.addEventListener('DOMContentLoaded', function() {
            loadUsers();
            loadTimesheets();
            loadReminderHistory();
            loadMessageTemplates();
            setupEventListeners();
            populateTimesheetTable();
            populateHistoryTable();
        });

        // Load users from localStorage
        function loadUsers() {
            users = JSON.parse(localStorage.getItem('users')) || [];
        }

        // Load or generate timesheet data
        function loadTimesheets() {
            timesheets = JSON.parse(localStorage.getItem('timesheets')) || generateSampleTimesheets();
            localStorage.setItem('timesheets', JSON.stringify(timesheets));
        }

        // Generate sample timesheet data
        function generateSampleTimesheets() {
            const sampleTimesheets = [];
            const statuses = ['pending', 'overdue', 'submitted'];
            const departments = ['Revenue', 'IT', 'HR', 'Finance'];

            users.forEach((user, index) => {
                const timesheet = {
                    id: `TS${String(index + 1).padStart(3, '0')}`,
                    userId: user.username,
                    userName: `${user.firstName} ${user.lastName}`,
                    department: departments[Math.floor(Math.random() * departments.length)],
                    period: getCurrentPeriod(),
                    status: statuses[Math.floor(Math.random() * statuses.length)],
                    dueDate: getNextFriday(),
                    lastReminder: getRandomPastDate(),
                    email: user.email
                };
                sampleTimesheets.push(timesheet);
            });

            return sampleTimesheets;
        }

        // Load reminder history
        function loadReminderHistory() {
            reminderHistory = JSON.parse(localStorage.getItem('reminderHistory')) || [];
        }

        // Load message templates
        function loadMessageTemplates() {
            messageTemplates = JSON.parse(localStorage.getItem('messageTemplates')) || {
                standard: `Dear {userName},

This is a reminder that your timesheet for the period {period} (ID: {timesheetId}) is due on {dueDate}.

Please submit your timesheet as soon as possible to avoid any delays in processing.

Thank you,
Revenue Section`,
                urgent: `URGENT: Dear {userName},

Your timesheet for the period {period} (ID: {timesheetId}) is OVERDUE. The due date was {dueDate}.

Please submit your timesheet immediately to avoid further delays.

Thank you,
Revenue Section`,
                final: `FINAL NOTICE: Dear {userName},

This is your final notice regarding your overdue timesheet for the period {period} (ID: {timesheetId}).

Failure to submit your timesheet may result in delays to your salary processing.

Please submit immediately.

Revenue Section`
            };
        }

        // Setup event listeners
        function setupEventListeners() {
            // Filter and search
            document.getElementById('applyFilters').addEventListener('click', applyFilters);
            document.getElementById('userSearch').addEventListener('input', applyFilters);

            // Selection controls
            document.getElementById('selectAllCheckbox').addEventListener('change', toggleSelectAll);
            document.getElementById('selectAllBtn').addEventListener('click', selectAll);
            document.getElementById('clearSelectionBtn').addEventListener('click', clearSelection);

            // Template controls
            document.getElementById('templateSelect').addEventListener('change', onTemplateChange);
            document.getElementById('loadTemplateBtn').addEventListener('click', loadTemplate);
            document.getElementById('saveTemplateBtn').addEventListener('click', saveTemplate);

            // Send reminders
            document.getElementById('sendRemindersBtn').addEventListener('click', showMessagePreview);

            // Modal controls
            document.getElementById('closeSuccessModal').addEventListener('click', closeSuccessModal);
            document.getElementById('closePreviewModal').addEventListener('click', closePreviewModal);
            document.getElementById('cancelPreview').addEventListener('click', closePreviewModal);
            document.getElementById('confirmSend').addEventListener('click', sendReminders);

            // History controls
            document.getElementById('historySearch').addEventListener('input', filterHistory);
            document.getElementById('exportHistoryBtn').addEventListener('click', exportHistory);
        }

        // Populate timesheet table
        function populateTimesheetTable() {
            const tableBody = document.getElementById('timesheetTableBody');
            tableBody.innerHTML = '';

            const filteredTimesheets = getFilteredTimesheets();

            filteredTimesheets.forEach(timesheet => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap">
                        <input type="checkbox" class="timesheet-checkbox rounded" data-timesheet-id="${timesheet.id}" onchange="updateSelection()">
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${timesheet.userName}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${timesheet.department}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${timesheet.id}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${timesheet.period}</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusColor(timesheet.status)}">
                            ${timesheet.status.charAt(0).toUpperCase() + timesheet.status.slice(1)}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${timesheet.dueDate}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${timesheet.lastReminder || 'Never'}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <button onclick="viewUserHistory('${timesheet.userId}')" class="text-indigo-600 hover:text-indigo-900">View History</button>
                    </td>
                `;
                tableBody.appendChild(row);
            });
        }

        // Get filtered timesheets based on current filters
        function getFilteredTimesheets() {
            const searchTerm = document.getElementById('userSearch').value.toLowerCase();
            const statusFilter = document.getElementById('statusFilter').value;
            const departmentFilter = document.getElementById('departmentFilter').value;

            return timesheets.filter(timesheet => {
                const matchesSearch = timesheet.userName.toLowerCase().includes(searchTerm) ||
                                    timesheet.id.toLowerCase().includes(searchTerm);
                const matchesStatus = statusFilter === 'all' || timesheet.status === statusFilter;
                const matchesDepartment = departmentFilter === 'all' || timesheet.department === departmentFilter;

                return matchesSearch && matchesStatus && matchesDepartment;
            });
        }

        // Apply filters
        function applyFilters() {
            populateTimesheetTable();
            updateSelectionCount();
        }

        // Update selection when checkboxes change
        function updateSelection() {
            const checkboxes = document.querySelectorAll('.timesheet-checkbox:checked');
            selectedTimesheets = Array.from(checkboxes).map(cb => cb.dataset.timesheetId);
            updateSelectionCount();
            updateSendButton();
        }

        // Update selection count display
        function updateSelectionCount() {
            const count = selectedTimesheets.length;
            document.getElementById('selectionCount').textContent = `${count} selected`;

            const summary = count === 0 ? 'No users selected' :
                           count === 1 ? '1 user selected for reminder' :
                           `${count} users selected for reminder`;
            document.getElementById('reminderSummary').textContent = summary;
        }

        // Update send button state
        function updateSendButton() {
            const sendBtn = document.getElementById('sendRemindersBtn');
            sendBtn.disabled = selectedTimesheets.length === 0;
        }

        // Toggle select all
        function toggleSelectAll() {
            const selectAllCheckbox = document.getElementById('selectAllCheckbox');
            const checkboxes = document.querySelectorAll('.timesheet-checkbox');

            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAllCheckbox.checked;
            });

            updateSelection();
        }

        // Select all visible timesheets
        function selectAll() {
            const checkboxes = document.querySelectorAll('.timesheet-checkbox');
            checkboxes.forEach(checkbox => checkbox.checked = true);
            document.getElementById('selectAllCheckbox').checked = true;
            updateSelection();
        }

        // Clear all selections
        function clearSelection() {
            const checkboxes = document.querySelectorAll('.timesheet-checkbox');
            checkboxes.forEach(checkbox => checkbox.checked = false);
            document.getElementById('selectAllCheckbox').checked = false;
            updateSelection();
        }

        // Handle template selection change
        function onTemplateChange() {
            const templateSelect = document.getElementById('templateSelect');
            if (templateSelect.value !== 'custom') {
                loadTemplate();
            }
        }

        // Load selected template
        function loadTemplate() {
            const templateSelect = document.getElementById('templateSelect');
            const messageTextarea = document.getElementById('reminderMessage');

            if (messageTemplates[templateSelect.value]) {
                messageTextarea.value = messageTemplates[templateSelect.value];
            }
        }

        // Save current message as template
        function saveTemplate() {
            const templateName = prompt('Enter template name:');
            if (templateName) {
                const message = document.getElementById('reminderMessage').value;
                messageTemplates[templateName] = message;
                localStorage.setItem('messageTemplates', JSON.stringify(messageTemplates));

                // Add to template select
                const templateSelect = document.getElementById('templateSelect');
                const option = document.createElement('option');
                option.value = templateName;
                option.textContent = templateName;
                templateSelect.appendChild(option);

                alert('Template saved successfully!');
            }
        }

        // Show message preview before sending
        function showMessagePreview() {
            if (selectedTimesheets.length === 0) {
                alert('Please select at least one timesheet.');
                return;
            }

            const selectedTimesheetData = selectedTimesheets.map(id =>
                timesheets.find(t => t.id === id)
            ).filter(t => t !== undefined);

            // Update summary
            const count = selectedTimesheetData.length;
            const summary = `You are about to send reminders to ${count} user${count > 1 ? 's' : ''} for their timesheet submissions.`;
            document.getElementById('previewSummary').textContent = summary;
            document.getElementById('recipientCount').textContent = count;

            // Update confirm button text
            document.getElementById('confirmSendText').textContent = `Send ${count} Reminder${count > 1 ? 's' : ''}`;

            // Populate recipients list
            populateRecipientsList(selectedTimesheetData);

            // Show sample message (first recipient)
            const firstTimesheet = selectedTimesheetData[0];
            const sampleMessage = replacePlaceholders(document.getElementById('reminderMessage').value, firstTimesheet);
            document.getElementById('sampleRecipient').textContent = `${firstTimesheet.userName} (${firstTimesheet.email})`;
            document.getElementById('sampleMessage').textContent = sampleMessage;

            // Populate all messages
            populateAllMessages(selectedTimesheetData);

            // Show sample tab by default
            showPreviewTab('sample');

            document.getElementById('messagePreviewModal').classList.remove('hidden');
        }

        // Populate recipients list in preview
        function populateRecipientsList(timesheetData) {
            const recipientsList = document.getElementById('recipientsList');
            recipientsList.innerHTML = '';

            timesheetData.forEach(timesheet => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td class="px-4 py-2 text-sm text-gray-900">${timesheet.userName}</td>
                    <td class="px-4 py-2 text-sm text-gray-600">${timesheet.email}</td>
                    <td class="px-4 py-2 text-sm text-gray-900">${timesheet.id}</td>
                    <td class="px-4 py-2">
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusColor(timesheet.status)}">
                            ${timesheet.status.charAt(0).toUpperCase() + timesheet.status.slice(1)}
                        </span>
                    </td>
                `;
                recipientsList.appendChild(row);
            });
        }

        // Populate all messages preview
        function populateAllMessages(timesheetData) {
            const container = document.getElementById('allMessagesContainer');
            container.innerHTML = '';

            const messageTemplate = document.getElementById('reminderMessage').value;

            timesheetData.forEach((timesheet, index) => {
                const personalizedMessage = replacePlaceholders(messageTemplate, timesheet);

                const messageDiv = document.createElement('div');
                messageDiv.className = 'p-4 border border-gray-200 rounded-md bg-gray-50';
                messageDiv.innerHTML = `
                    <div class="mb-2 font-medium text-gray-900">
                        Message ${index + 1}: ${timesheet.userName} (${timesheet.email})
                    </div>
                    <div class="mb-2 text-sm text-gray-600">
                        <span class="font-medium">Timesheet:</span> ${timesheet.id} |
                        <span class="font-medium">Status:</span> ${timesheet.status}
                    </div>
                    <div class="text-sm text-gray-800 whitespace-pre-wrap border-t pt-2 mt-2">
                        ${personalizedMessage}
                    </div>
                `;
                container.appendChild(messageDiv);
            });
        }

        // Show preview tab (sample or all)
        function showPreviewTab(tabName) {
            // Hide all tab contents
            document.getElementById('samplePreviewContent').classList.add('hidden');
            document.getElementById('allPreviewContent').classList.add('hidden');

            // Remove active class from all tabs
            document.getElementById('samplePreviewTab').classList.remove('border-indigo-500', 'text-indigo-600');
            document.getElementById('samplePreviewTab').classList.add('border-transparent', 'text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300');
            document.getElementById('allPreviewTab').classList.remove('border-indigo-500', 'text-indigo-600');
            document.getElementById('allPreviewTab').classList.add('border-transparent', 'text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300');

            // Show selected tab content and mark tab as active
            if (tabName === 'sample') {
                document.getElementById('samplePreviewContent').classList.remove('hidden');
                document.getElementById('samplePreviewTab').classList.remove('border-transparent', 'text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300');
                document.getElementById('samplePreviewTab').classList.add('border-indigo-500', 'text-indigo-600');
            } else if (tabName === 'all') {
                document.getElementById('allPreviewContent').classList.remove('hidden');
                document.getElementById('allPreviewTab').classList.remove('border-transparent', 'text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300');
                document.getElementById('allPreviewTab').classList.add('border-indigo-500', 'text-indigo-600');
            }
        }

        // Send reminders to selected users
        function sendReminders() {
            const message = document.getElementById('reminderMessage').value;
            const sentBy = 'Revenue Section Officer'; // This could be dynamic based on logged-in user
            const sentAt = new Date().toISOString();

            selectedTimesheets.forEach(timesheetId => {
                const timesheet = timesheets.find(t => t.id === timesheetId);
                if (timesheet) {
                    const personalizedMessage = replacePlaceholders(message, timesheet);

                    // Add to reminder history
                    reminderHistory.unshift({
                        id: Date.now() + Math.random(),
                        timesheetId: timesheet.id,
                        userId: timesheet.userId,
                        userName: timesheet.userName,
                        message: personalizedMessage,
                        sentBy: sentBy,
                        sentAt: sentAt,
                        status: 'sent'
                    });

                    // Update timesheet last reminder date
                    timesheet.lastReminder = new Date().toLocaleDateString();
                }
            });

            // Save to localStorage
            localStorage.setItem('reminderHistory', JSON.stringify(reminderHistory));
            localStorage.setItem('timesheets', JSON.stringify(timesheets));

            // Close preview modal and show success
            closePreviewModal();
            showSuccessModal();

            // Clear selection and refresh tables
            clearSelection();
            populateTimesheetTable();
            populateHistoryTable();
        }

        // Replace placeholders in message template
        function replacePlaceholders(message, timesheet) {
            return message
                .replace(/{userName}/g, timesheet.userName)
                .replace(/{timesheetId}/g, timesheet.id)
                .replace(/{period}/g, timesheet.period)
                .replace(/{dueDate}/g, timesheet.dueDate)
                .replace(/{department}/g, timesheet.department);
        }

        // Show success modal
        function showSuccessModal() {
            const count = selectedTimesheets.length;
            const message = `${count} reminder${count > 1 ? 's have' : ' has'} been sent successfully.`;
            document.getElementById('successMessage').textContent = message;
            document.getElementById('successModal').classList.remove('hidden');
        }

        // Close success modal
        function closeSuccessModal() {
            document.getElementById('successModal').classList.add('hidden');
        }

        // Close preview modal
        function closePreviewModal() {
            document.getElementById('messagePreviewModal').classList.add('hidden');
        }

        // Populate history table
        function populateHistoryTable() {
            const tableBody = document.getElementById('historyTableBody');
            tableBody.innerHTML = '';

            const filteredHistory = getFilteredHistory();

            filteredHistory.slice(0, 50).forEach(entry => { // Show last 50 entries
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${formatDateTime(entry.sentAt)}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${entry.userName}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${entry.timesheetId}</td>
                    <td class="px-6 py-4 text-sm text-gray-900 max-w-xs truncate">${entry.message.substring(0, 50)}...</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${entry.sentBy}</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                            ${entry.status}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <button onclick="viewFullMessage('${entry.id}')" class="text-indigo-600 hover:text-indigo-900">View Full</button>
                    </td>
                `;
                tableBody.appendChild(row);
            });
        }

        // Get filtered history
        function getFilteredHistory() {
            const searchTerm = document.getElementById('historySearch').value.toLowerCase();

            return reminderHistory.filter(entry =>
                entry.userName.toLowerCase().includes(searchTerm) ||
                entry.timesheetId.toLowerCase().includes(searchTerm) ||
                entry.message.toLowerCase().includes(searchTerm)
            );
        }

        // Filter history based on search
        function filterHistory() {
            populateHistoryTable();
        }

        // View user history
        function viewUserHistory(userId) {
            const userHistory = reminderHistory.filter(entry => entry.userId === userId);
            // This could open a modal or navigate to a detailed view
            alert(`Found ${userHistory.length} reminder(s) for this user.`);
        }

        // View full message
        function viewFullMessage(entryId) {
            const entry = reminderHistory.find(e => e.id == entryId);
            if (entry) {
                alert(`Full Message:\n\n${entry.message}`);
            }
        }

        // Export history to CSV
        function exportHistory() {
            const csvContent = generateHistoryCSV();
            downloadCSV(csvContent, 'reminder_history.csv');
        }

        // Generate CSV content for history
        function generateHistoryCSV() {
            const headers = ['Date/Time', 'User', 'Timesheet ID', 'Message', 'Sent By', 'Status'];
            const rows = reminderHistory.map(entry => [
                formatDateTime(entry.sentAt),
                entry.userName,
                entry.timesheetId,
                `"${entry.message.replace(/"/g, '""')}"`,
                entry.sentBy,
                entry.status
            ]);

            return [headers, ...rows].map(row => row.join(',')).join('\n');
        }

        // Download CSV file
        function downloadCSV(content, filename) {
            const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', filename);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // Utility functions
        function getStatusColor(status) {
            switch (status) {
                case 'pending': return 'bg-yellow-100 text-yellow-800';
                case 'overdue': return 'bg-red-100 text-red-800';
                case 'submitted': return 'bg-green-100 text-green-800';
                default: return 'bg-gray-100 text-gray-800';
            }
        }

        function getCurrentPeriod() {
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            return `${year}-${month}`;
        }

        function getNextFriday() {
            const today = new Date();
            const daysUntilFriday = (5 - today.getDay() + 7) % 7;
            const nextFriday = new Date(today);
            nextFriday.setDate(today.getDate() + daysUntilFriday);
            return nextFriday.toLocaleDateString();
        }

        function getRandomPastDate() {
            const today = new Date();
            const pastDate = new Date(today);
            pastDate.setDate(today.getDate() - Math.floor(Math.random() * 30));
            return pastDate.toLocaleDateString();
        }

        function formatDateTime(isoString) {
            return new Date(isoString).toLocaleString();
        }
    </script>
</body>
</html>

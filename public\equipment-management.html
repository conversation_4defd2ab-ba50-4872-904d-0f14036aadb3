<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Equipment Availability Management - OFCA Portal</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="css/ofca-styles.css">
    <script src="js/header.js"></script>
    <script src="js/footer.js"></script>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="p-8">
        <div class="bg-white p-8 rounded-lg shadow-lg w-full max-w-7xl mx-auto">
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-2xl font-bold">Equipment Availability Management</h1>
                <div class="flex space-x-3">
                    <button id="addEquipmentBtn" class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Add Equipment
                    </button>
                    <button id="calendarViewBtn" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                        Calendar View
                    </button>
                    <a href="index.html" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">Back to Dashboard</a>
                </div>
            </div>

            <!-- Equipment List View -->
            <div id="equipmentListView" class="mb-6">
                <div class="mb-4 bg-gray-50 p-4 rounded-lg">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label for="equipmentSearch" class="block text-sm font-medium text-gray-700">Search Equipment</label>
                            <input type="text" id="equipmentSearch" placeholder="Search by name or ID..." class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                        </div>
                        <div>
                            <label for="categoryFilter" class="block text-sm font-medium text-gray-700">Category</label>
                            <select id="categoryFilter" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                <option value="all">All Categories</option>
                                <option value="laptop">Laptops</option>
                                <option value="projector">Projectors</option>
                                <option value="camera">Cameras</option>
                                <option value="audio">Audio Equipment</option>
                                <option value="network">Network Equipment</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                        <div>
                            <label for="statusFilter" class="block text-sm font-medium text-gray-700">Status</label>
                            <select id="statusFilter" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                <option value="all">All Status</option>
                                <option value="available">Available</option>
                                <option value="loaned">Currently Loaned</option>
                                <option value="maintenance">Under Maintenance</option>
                                <option value="retired">Retired</option>
                            </select>
                        </div>
                    </div>
                    <div class="mt-4 flex justify-end">
                        <button id="clearFilters" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                            Clear Filters
                        </button>
                    </div>
                </div>

                <!-- Equipment Table -->
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Equipment ID</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Current Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Current Loan</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody id="equipmentTableBody" class="bg-white divide-y divide-gray-200">
                            <!-- Equipment rows will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>

                <!-- Equipment Count -->
                <div class="mt-4 text-sm text-gray-500">
                    Total Equipment: <span id="equipmentCount">0</span>
                </div>
            </div>

            <!-- Calendar View -->
            <div id="calendarView" class="hidden">
                <div class="mb-4 flex justify-between items-center">
                    <h2 class="text-xl font-semibold">Equipment Loan Calendar</h2>
                    <div class="flex space-x-2">
                        <button id="prevMonth" class="px-3 py-1 border border-gray-300 rounded-md text-sm hover:bg-gray-50">Previous</button>
                        <span id="currentMonth" class="px-4 py-1 text-sm font-medium"></span>
                        <button id="nextMonth" class="px-3 py-1 border border-gray-300 rounded-md text-sm hover:bg-gray-50">Next</button>
                    </div>
                    <button id="backToListBtn" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700">
                        Back to List
                    </button>
                </div>

                <!-- Calendar Grid -->
                <div class="bg-white border border-gray-200 rounded-lg overflow-hidden">
                    <div class="grid grid-cols-7 gap-0">
                        <!-- Calendar headers -->
                        <div class="bg-gray-50 px-4 py-2 text-center text-sm font-medium text-gray-700 border-b border-r border-gray-200">Sun</div>
                        <div class="bg-gray-50 px-4 py-2 text-center text-sm font-medium text-gray-700 border-b border-r border-gray-200">Mon</div>
                        <div class="bg-gray-50 px-4 py-2 text-center text-sm font-medium text-gray-700 border-b border-r border-gray-200">Tue</div>
                        <div class="bg-gray-50 px-4 py-2 text-center text-sm font-medium text-gray-700 border-b border-r border-gray-200">Wed</div>
                        <div class="bg-gray-50 px-4 py-2 text-center text-sm font-medium text-gray-700 border-b border-r border-gray-200">Thu</div>
                        <div class="bg-gray-50 px-4 py-2 text-center text-sm font-medium text-gray-700 border-b border-r border-gray-200">Fri</div>
                        <div class="bg-gray-50 px-4 py-2 text-center text-sm font-medium text-gray-700 border-b border-gray-200">Sat</div>
                    </div>
                    <div id="calendarGrid" class="grid grid-cols-7 gap-0">
                        <!-- Calendar days will be populated by JavaScript -->
                    </div>
                </div>

                <!-- Calendar Legend -->
                <div class="mt-4 flex space-x-6 text-sm">
                    <div class="flex items-center">
                        <div class="w-4 h-4 bg-green-200 border border-green-300 rounded mr-2"></div>
                        <span>Available</span>
                    </div>
                    <div class="flex items-center">
                        <div class="w-4 h-4 bg-red-200 border border-red-300 rounded mr-2"></div>
                        <span>Fully Booked</span>
                    </div>
                    <div class="flex items-center">
                        <div class="w-4 h-4 bg-yellow-200 border border-yellow-300 rounded mr-2"></div>
                        <span>Partially Booked</span>
                    </div>
                    <div class="flex items-center">
                        <div class="w-4 h-4 bg-gray-200 border border-gray-300 rounded mr-2"></div>
                        <span>Maintenance</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let equipment = [];
        let loanSchedules = [];
        let currentDate = new Date();
        let currentView = 'list'; // 'list' or 'calendar'

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            loadEquipment();
            loadLoanSchedules();
            setupEventListeners();
            populateEquipmentTable();
        });

        // Load equipment from localStorage
        function loadEquipment() {
            const storedEquipment = localStorage.getItem('equipment');
            if (storedEquipment) {
                equipment = JSON.parse(storedEquipment);
            } else {
                // Initialize with default equipment
                equipment = getDefaultEquipment();
                saveEquipment();
            }
        }

        // Load loan schedules from localStorage
        function loadLoanSchedules() {
            const storedSchedules = localStorage.getItem('loanSchedules');
            if (storedSchedules) {
                loanSchedules = JSON.parse(storedSchedules);
            } else {
                // Initialize with sample loan schedules
                loanSchedules = getDefaultLoanSchedules();
                saveLoanSchedules();
            }
        }

        // Get default equipment data
        function getDefaultEquipment() {
            return [
                {
                    id: 'EQ001',
                    name: 'Dell Laptop XPS 13',
                    category: 'laptop',
                    description: 'High-performance laptop for presentations and development',
                    status: 'available',
                    location: 'IT Storage Room A',
                    purchaseDate: '2023-01-15',
                    warrantyExpiry: '2026-01-15',
                    specifications: 'Intel i7, 16GB RAM, 512GB SSD',
                    createdDate: new Date().toISOString(),
                    lastModified: new Date().toISOString()
                },
                {
                    id: 'EQ002',
                    name: 'Epson Projector EB-X41',
                    category: 'projector',
                    description: 'Portable projector for meetings and presentations',
                    status: 'available',
                    location: 'IT Storage Room B',
                    purchaseDate: '2023-03-20',
                    warrantyExpiry: '2026-03-20',
                    specifications: '3600 lumens, XGA resolution, HDMI/VGA inputs',
                    createdDate: new Date().toISOString(),
                    lastModified: new Date().toISOString()
                },
                {
                    id: 'EQ003',
                    name: 'Canon DSLR Camera EOS 90D',
                    category: 'camera',
                    description: 'Professional camera for events and documentation',
                    status: 'loaned',
                    location: 'IT Storage Room A',
                    purchaseDate: '2023-05-10',
                    warrantyExpiry: '2025-05-10',
                    specifications: '32.5MP APS-C sensor, 4K video recording',
                    createdDate: new Date().toISOString(),
                    lastModified: new Date().toISOString()
                }
            ];
        }

        // Get default loan schedules
        function getDefaultLoanSchedules() {
            const today = new Date();
            const tomorrow = new Date(today);
            tomorrow.setDate(tomorrow.getDate() + 1);
            const nextWeek = new Date(today);
            nextWeek.setDate(nextWeek.getDate() + 7);

            return [
                {
                    id: 'loan_001',
                    equipmentId: 'EQ003',
                    borrowerName: 'John Doe',
                    borrowerDepartment: 'Marketing',
                    startDate: today.toISOString().split('T')[0],
                    endDate: tomorrow.toISOString().split('T')[0],
                    purpose: 'Product photography session',
                    status: 'active',
                    approvedBy: 'IT Admin',
                    createdDate: new Date().toISOString()
                },
                {
                    id: 'loan_002',
                    equipmentId: 'EQ002',
                    borrowerName: 'Jane Smith',
                    borrowerDepartment: 'HR',
                    startDate: nextWeek.toISOString().split('T')[0],
                    endDate: new Date(nextWeek.getTime() + 2 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                    purpose: 'Training presentation',
                    status: 'scheduled',
                    approvedBy: 'IT Admin',
                    createdDate: new Date().toISOString()
                }
            ];
        }

        // Save equipment to localStorage
        function saveEquipment() {
            localStorage.setItem('equipment', JSON.stringify(equipment));
        }

        // Save loan schedules to localStorage
        function saveLoanSchedules() {
            localStorage.setItem('loanSchedules', JSON.stringify(loanSchedules));
        }

        // Setup event listeners
        function setupEventListeners() {
            // View toggle buttons
            document.getElementById('calendarViewBtn').addEventListener('click', showCalendarView);
            document.getElementById('backToListBtn').addEventListener('click', showListView);

            // Equipment management
            document.getElementById('addEquipmentBtn').addEventListener('click', openAddEquipmentModal);

            // Filters
            document.getElementById('equipmentSearch').addEventListener('input', applyFilters);
            document.getElementById('categoryFilter').addEventListener('change', applyFilters);
            document.getElementById('statusFilter').addEventListener('change', applyFilters);
            document.getElementById('clearFilters').addEventListener('click', clearFilters);

            // Calendar navigation
            document.getElementById('prevMonth').addEventListener('click', () => navigateMonth(-1));
            document.getElementById('nextMonth').addEventListener('click', () => navigateMonth(1));
        }

        // Show calendar view
        function showCalendarView() {
            currentView = 'calendar';
            document.getElementById('equipmentListView').classList.add('hidden');
            document.getElementById('calendarView').classList.remove('hidden');
            generateCalendar();
        }

        // Show list view
        function showListView() {
            currentView = 'list';
            document.getElementById('calendarView').classList.add('hidden');
            document.getElementById('equipmentListView').classList.remove('hidden');
        }

        // Apply filters to equipment list
        function applyFilters() {
            populateEquipmentTable();
        }

        // Clear all filters
        function clearFilters() {
            document.getElementById('equipmentSearch').value = '';
            document.getElementById('categoryFilter').value = 'all';
            document.getElementById('statusFilter').value = 'all';
            populateEquipmentTable();
        }

        // Get filtered equipment
        function getFilteredEquipment() {
            const searchTerm = document.getElementById('equipmentSearch').value.toLowerCase();
            const categoryFilter = document.getElementById('categoryFilter').value;
            const statusFilter = document.getElementById('statusFilter').value;

            return equipment.filter(item => {
                // Search filter
                const searchMatch = item.name.toLowerCase().includes(searchTerm) ||
                                  item.id.toLowerCase().includes(searchTerm) ||
                                  item.description.toLowerCase().includes(searchTerm);

                // Category filter
                const categoryMatch = categoryFilter === 'all' || item.category === categoryFilter;

                // Status filter
                const statusMatch = statusFilter === 'all' || item.status === statusFilter;

                return searchMatch && categoryMatch && statusMatch;
            });
        }

        // Populate equipment table
        function populateEquipmentTable() {
            const tableBody = document.getElementById('equipmentTableBody');
            const filteredEquipment = getFilteredEquipment();

            // Clear existing rows
            tableBody.innerHTML = '';

            if (filteredEquipment.length === 0) {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td colspan="6" class="px-6 py-4 text-center text-sm text-gray-500">
                        No equipment found matching the current filters.
                    </td>
                `;
                tableBody.appendChild(row);
            } else {
                filteredEquipment.forEach(item => {
                    const row = document.createElement('tr');
                    row.className = 'hover:bg-gray-50';

                    // Get current loan info
                    const currentLoan = getCurrentLoan(item.id);
                    const currentLoanText = currentLoan ?
                        `${currentLoan.borrowerName} (${formatDate(currentLoan.startDate)} - ${formatDate(currentLoan.endDate)})` :
                        'None';

                    // Format category
                    const categoryColors = {
                        'laptop': 'bg-blue-100 text-blue-800',
                        'projector': 'bg-green-100 text-green-800',
                        'camera': 'bg-purple-100 text-purple-800',
                        'audio': 'bg-yellow-100 text-yellow-800',
                        'network': 'bg-indigo-100 text-indigo-800',
                        'other': 'bg-gray-100 text-gray-800'
                    };

                    const categoryClass = categoryColors[item.category] || 'bg-gray-100 text-gray-800';
                    const categoryText = item.category.charAt(0).toUpperCase() + item.category.slice(1);

                    // Format status
                    const statusColors = {
                        'available': 'bg-green-100 text-green-800',
                        'loaned': 'bg-red-100 text-red-800',
                        'maintenance': 'bg-yellow-100 text-yellow-800',
                        'retired': 'bg-gray-100 text-gray-800'
                    };

                    const statusClass = statusColors[item.status] || 'bg-gray-100 text-gray-800';
                    const statusText = item.status.charAt(0).toUpperCase() + item.status.slice(1);

                    row.innerHTML = `
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-mono font-bold text-gray-900">${item.id}</div>
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm font-medium text-gray-900">${item.name}</div>
                            <div class="text-sm text-gray-500 truncate max-w-xs">${item.description}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${categoryClass}">
                                ${categoryText}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${statusClass}">
                                ${statusText}
                            </span>
                        </td>
                        <td class="px-6 py-4 text-sm text-gray-900">
                            <div class="max-w-xs truncate">${currentLoanText}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                            <button onclick="viewEquipmentDetails('${item.id}')" class="text-indigo-600 hover:text-indigo-900">
                                Details
                            </button>
                            <button onclick="manageAvailability('${item.id}')" class="text-green-600 hover:text-green-900">
                                Availability
                            </button>
                            <button onclick="editEquipment('${item.id}')" class="text-blue-600 hover:text-blue-900">
                                Edit
                            </button>
                        </td>
                    `;

                    tableBody.appendChild(row);
                });
            }

            // Update equipment count
            document.getElementById('equipmentCount').textContent = filteredEquipment.length;
        }

        // Get current loan for equipment
        function getCurrentLoan(equipmentId) {
            const today = new Date().toISOString().split('T')[0];
            return loanSchedules.find(loan =>
                loan.equipmentId === equipmentId &&
                loan.status === 'active' &&
                loan.startDate <= today &&
                loan.endDate >= today
            );
        }

        // Format date for display
        function formatDate(dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);
            return date.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        }

        // Get category display name
        function getCategoryDisplayName(category) {
            const categoryNames = {
                'laptop': 'Laptops',
                'projector': 'Projectors',
                'camera': 'Cameras',
                'audio': 'Audio Equipment',
                'network': 'Network Equipment',
                'other': 'Other Equipment'
            };
            return categoryNames[category] || category;
        }

        // Calendar functionality
        function generateCalendar() {
            const year = currentDate.getFullYear();
            const month = currentDate.getMonth();

            // Update month display
            document.getElementById('currentMonth').textContent =
                new Date(year, month).toLocaleDateString('en-US', { year: 'numeric', month: 'long' });

            const firstDay = new Date(year, month, 1);
            const lastDay = new Date(year, month + 1, 0);
            const startDate = new Date(firstDay);
            startDate.setDate(startDate.getDate() - firstDay.getDay());

            const calendarGrid = document.getElementById('calendarGrid');
            calendarGrid.innerHTML = '';

            // Generate 42 days (6 weeks)
            for (let i = 0; i < 42; i++) {
                const cellDate = new Date(startDate);
                cellDate.setDate(startDate.getDate() + i);

                const dayCell = document.createElement('div');
                dayCell.className = 'min-h-24 p-2 border-b border-r border-gray-200 relative';

                if (cellDate.getMonth() !== month) {
                    dayCell.classList.add('bg-gray-50', 'text-gray-400');
                }

                // Day number
                const dayNumber = document.createElement('div');
                dayNumber.className = 'text-sm font-medium mb-1';
                dayNumber.textContent = cellDate.getDate();
                dayCell.appendChild(dayNumber);

                // Get loans for this date
                const dateString = cellDate.toISOString().split('T')[0];
                const dayLoans = getLoansByDate(dateString);

                // Add loan indicators
                dayLoans.forEach((loan, index) => {
                    if (index < 3) { // Show max 3 loans per day
                        const loanDiv = document.createElement('div');
                        loanDiv.className = 'text-xs p-1 mb-1 rounded truncate cursor-pointer';

                        const equipmentItem = equipment.find(eq => eq.id === loan.equipmentId);
                        const equipmentName = equipmentItem ? equipmentItem.name : 'Unknown';

                        if (loan.status === 'active') {
                            loanDiv.classList.add('bg-red-100', 'text-red-800');
                        } else {
                            loanDiv.classList.add('bg-yellow-100', 'text-yellow-800');
                        }

                        loanDiv.textContent = `${equipmentName.substring(0, 15)}...`;
                        loanDiv.title = `${equipmentName} - ${loan.borrowerName}`;
                        loanDiv.onclick = () => viewLoanDetails(loan.id);

                        dayCell.appendChild(loanDiv);
                    }
                });

                // Show "+" if more loans exist
                if (dayLoans.length > 3) {
                    const moreDiv = document.createElement('div');
                    moreDiv.className = 'text-xs text-gray-500 cursor-pointer';
                    moreDiv.textContent = `+${dayLoans.length - 3} more`;
                    moreDiv.onclick = () => showDayDetails(dateString);
                    dayCell.appendChild(moreDiv);
                }

                // Add click handler for day
                dayCell.onclick = (e) => {
                    if (e.target === dayCell || e.target === dayNumber) {
                        showDayDetails(dateString);
                    }
                };

                calendarGrid.appendChild(dayCell);
            }
        }

        // Navigate calendar months
        function navigateMonth(direction) {
            currentDate.setMonth(currentDate.getMonth() + direction);
            generateCalendar();
        }

        // Get loans by date
        function getLoansByDate(dateString) {
            return loanSchedules.filter(loan => {
                return dateString >= loan.startDate && dateString <= loan.endDate;
            });
        }

        // Equipment management functions
        function viewEquipmentDetails(equipmentId) {
            const item = equipment.find(eq => eq.id === equipmentId);
            if (!item) return;

            // Create and show equipment details modal
            showEquipmentDetailsModal(item);
        }

        function manageAvailability(equipmentId) {
            const item = equipment.find(eq => eq.id === equipmentId);
            if (!item) return;

            // Create and show availability management modal
            showAvailabilityModal(item);
        }

        function editEquipment(equipmentId) {
            const item = equipment.find(eq => eq.id === equipmentId);
            if (!item) return;

            // Create and show edit equipment modal
            showEditEquipmentModal(item);
        }

        function openAddEquipmentModal() {
            // Create and show add equipment modal
            showAddEquipmentModal();
        }

        function viewLoanDetails(loanId) {
            const loan = loanSchedules.find(l => l.id === loanId);
            if (!loan) return;

            // Create and show loan details modal
            showLoanDetailsModal(loan);
        }

        function showDayDetails(dateString) {
            const dayLoans = getLoansByDate(dateString);
            // Create and show day details modal
            showDayDetailsModal(dateString, dayLoans);
        }

        // Placeholder modal functions (to be implemented)
        function showEquipmentDetailsModal(item) {
            alert(`Equipment Details:\nID: ${item.id}\nName: ${item.name}\nStatus: ${item.status}\nLocation: ${item.location}`);
        }

        function showAvailabilityModal(item) {
            const newStatus = prompt(`Current status: ${item.status}\nEnter new status (available/loaned/maintenance/retired):`, item.status);
            if (newStatus && ['available', 'loaned', 'maintenance', 'retired'].includes(newStatus)) {
                item.status = newStatus;
                item.lastModified = new Date().toISOString();
                saveEquipment();
                populateEquipmentTable();
                if (currentView === 'calendar') {
                    generateCalendar();
                }
            }
        }

        function showEditEquipmentModal(item) {
            const newName = prompt('Equipment Name:', item.name);
            if (newName) {
                item.name = newName;
                item.lastModified = new Date().toISOString();
                saveEquipment();
                populateEquipmentTable();
            }
        }

        function showAddEquipmentModal() {
            const name = prompt('Equipment Name:');
            const category = prompt('Category (laptop/projector/camera/audio/network/other):');

            if (name && category) {
                const newEquipment = {
                    id: 'EQ' + String(Date.now()).slice(-3),
                    name: name,
                    category: category,
                    description: '',
                    status: 'available',
                    location: 'IT Storage',
                    purchaseDate: new Date().toISOString().split('T')[0],
                    warrantyExpiry: '',
                    specifications: '',
                    createdDate: new Date().toISOString(),
                    lastModified: new Date().toISOString()
                };

                equipment.push(newEquipment);
                saveEquipment();
                populateEquipmentTable();
            }
        }

        function showLoanDetailsModal(loan) {
            const equipmentItem = equipment.find(eq => eq.id === loan.equipmentId);
            const equipmentName = equipmentItem ? equipmentItem.name : 'Unknown Equipment';

            alert(`Loan Details:\nEquipment: ${equipmentName}\nBorrower: ${loan.borrowerName}\nDepartment: ${loan.borrowerDepartment}\nPeriod: ${formatDate(loan.startDate)} - ${formatDate(loan.endDate)}\nPurpose: ${loan.purpose}\nStatus: ${loan.status}`);
        }

        function showDayDetailsModal(dateString, dayLoans) {
            const formattedDate = formatDate(dateString);
            let message = `Loans for ${formattedDate}:\n\n`;

            if (dayLoans.length === 0) {
                message += 'No equipment loans scheduled for this day.';
            } else {
                dayLoans.forEach(loan => {
                    const equipmentItem = equipment.find(eq => eq.id === loan.equipmentId);
                    const equipmentName = equipmentItem ? equipmentItem.name : 'Unknown Equipment';
                    message += `• ${equipmentName} - ${loan.borrowerName} (${loan.borrowerDepartment})\n`;
                });
            }

            alert(message);
        }
    </script>
</body>
</html>

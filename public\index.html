<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>View User Accounts - OFCA Portal</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="css/ofca-styles.css">
    <script src="js/header.js"></script>
    <script src="js/footer.js"></script>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="p-8">
    <div class="bg-white p-8 rounded-lg shadow-lg w-full max-w-4xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold">User Account Management</h1>
            <div class="flex space-x-4">
                <a href="permissions.html" class="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700">Manage Permissions</a>
                <button id="manageSupervisorBtn" class="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700">Manage Supervisor</button>
                <a href="create-account.html" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700">Create Account</a>
            </div>
        </div>

        <!-- Search and Filter Section -->
        <div class="mb-6">
            <div class="flex flex-col md:flex-row md:items-end md:justify-between gap-4">
                <div class="flex-1">
                    <label for="search" class="block text-sm font-medium text-gray-700">Search Staff</label>
                    <input type="text" id="search" placeholder="Enter name or username..." class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                </div>
                <div class="flex gap-4 flex-wrap">
                    <div>
                        <label for="rankFilter" class="block text-sm font-medium text-gray-700">Filter By Rank</label>
                        <select id="rankFilter" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                            <option value="all">All Ranks</option>
                            <!-- Rank options will be populated dynamically -->
                        </select>
                    </div>
                    <div class="flex flex-col">
                        <label for="statusFilter" class="block text-sm font-medium text-gray-700">Filter By Status</label>
                        <div class="flex gap-2 mt-1">
                            <select id="statusFilter" class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                <option value="all">All Statuses</option>
                                <option value="active">Active</option>
                                <option value="pending">Pending</option>
                                <option value="resigned">Resigned</option>
                                <option value="disabled">Disabled</option>
                            </select>
                            <button id="clearFilters" class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                Clear
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="mt-4 text-sm text-gray-500">
                <span id="filterSummary">Showing all users</span> | <span id="resultCount"></span>
            </div>
        </div>

        <!-- User Account List -->
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200" id="userTable">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" data-sort="name">
                            <div class="flex items-center">
                                Name
                                <span class="sort-icon ml-1 invisible">▲</span>
                            </div>
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" data-sort="username">
                            <div class="flex items-center">
                                Username
                                <span class="sort-icon ml-1 invisible">▲</span>
                            </div>
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" data-sort="rank">
                            <div class="flex items-center">
                                Rank
                                <span class="sort-icon ml-1 invisible">▲</span>
                            </div>
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" data-sort="status">
                            <div class="flex items-center">
                                Status
                                <span class="sort-icon ml-1 invisible">▲</span>
                            </div>
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200" id="userTableBody">
                    <!-- User rows will be dynamically generated by JavaScript -->
                </tbody>
            </table>
        </div>
    </div>

    <!-- Supervisor Management Modal -->
    <div id="supervisorModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden">
        <div class="bg-white p-6 rounded-lg shadow-xl max-w-4xl w-full">
            <div class="flex justify-between items-center border-b pb-3 mb-4">
                <h3 class="text-lg font-medium text-gray-900">Manage Supervisor Lists</h3>
                <button type="button" id="closeSupervisorModal" class="text-gray-400 hover:text-gray-500">
                    <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Appraising Officer List -->
                <div>
                    <h4 class="font-medium text-gray-900 mb-3">Appraising Officer List</h4>
                    <div class="mb-4">
                        <div class="flex space-x-2">
                            <select id="appraisingOfficerAdd" class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                <option value="">Select User to Add</option>
                                <!-- Options will be populated dynamically -->
                            </select>
                            <button type="button" id="addAppraisingOfficerBtn" class="px-3 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                                Add
                            </button>
                        </div>
                    </div>
                    <div class="border rounded-md overflow-hidden">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rank</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                                </tr>
                            </thead>
                            <tbody id="appraisingOfficerList" class="bg-white divide-y divide-gray-200">
                                <!-- List will be populated dynamically -->
                                <tr>
                                    <td colspan="3" class="px-6 py-4 text-center text-sm text-gray-500">No officers added yet.</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Countersigning Officer List -->
                <div>
                    <h4 class="font-medium text-gray-900 mb-3">Countersigning Officer List</h4>
                    <div class="mb-4">
                        <div class="flex space-x-2">
                            <select id="countersigningOfficerAdd" class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                <option value="">Select User to Add</option>
                                <!-- Options will be populated dynamically -->
                            </select>
                            <button type="button" id="addCountersigningOfficerBtn" class="px-3 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                                Add
                            </button>
                        </div>
                    </div>
                    <div class="border rounded-md overflow-hidden">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rank</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                                </tr>
                            </thead>
                            <tbody id="countersigningOfficerList" class="bg-white divide-y divide-gray-200">
                                <!-- List will be populated dynamically -->
                                <tr>
                                    <td colspan="3" class="px-6 py-4 text-center text-sm text-gray-500">No officers added yet.</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <div class="mt-6 flex justify-end">
                <button type="button" id="saveSupervisorChanges" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Save Changes
                </button>
            </div>
        </div>
    </div>

    <script>
        // Initialize default users if none exist
        const defaultUsers = [
            {
                id: '1',
                firstName: 'John',
                lastName: 'Doe',
                username: 'jdoe',
                email: '<EMAIL>',
                phone: '(*************',
                department: 'IT',
                jobTitle: 'Software Engineer',
                role: 'Admin',
                status: 'active',
                accessLevel: 'Full',
                notes: 'System administrator with full access'
            },
            {
                id: '2',
                firstName: 'Jane',
                lastName: 'Smith',
                username: 'jsmith',
                email: '<EMAIL>',
                phone: '(*************',
                department: 'HR',
                jobTitle: 'HR Manager',
                role: 'User',
                status: 'pending',
                accessLevel: 'Standard',
                notes: 'Awaiting approval from department head'
            },
            {
                id: '3',
                firstName: 'Alice',
                lastName: 'Johnson',
                username: 'ajohnson',
                email: '<EMAIL>',
                phone: '(*************',
                department: 'Finance',
                jobTitle: 'Accountant',
                role: 'User',
                status: 'resigned',
                accessLevel: 'Basic',
                notes: 'Left the company on 2023-12-15'
            },
            {
                id: '4',
                firstName: 'Robert',
                lastName: 'Williams',
                username: 'rwilliams',
                email: '<EMAIL>',
                phone: '(*************',
                department: 'Sales',
                jobTitle: 'Sales Representative',
                role: 'User',
                status: 'disabled',
                accessLevel: 'Standard',
                notes: 'Account temporarily disabled due to extended leave'
            }
        ];

        // Get users from localStorage or use defaults
        let users = JSON.parse(localStorage.getItem('users')) || defaultUsers;

        // If users array is empty, use defaults
        if (users.length === 0) {
            users = defaultUsers;
            localStorage.setItem('users', JSON.stringify(users));
        }

        // Variables to track current sort and filter state
        let currentSort = { column: 'name', direction: 'asc' };
        let currentFilters = { search: '', rank: 'all', status: 'all' };

        // Function to populate rank filter options
        function populateRankFilter() {
            const rankFilter = document.getElementById('rankFilter');
            const uniqueRanks = new Set();

            // Get all unique ranks
            users.forEach(user => {
                if (user.rankName) {
                    uniqueRanks.add(user.rankName);
                }
            });

            // Clear existing options except the first one
            while (rankFilter.options.length > 1) {
                rankFilter.remove(1);
            }

            // Add options for each unique rank
            uniqueRanks.forEach(rank => {
                const option = document.createElement('option');
                option.value = rank;
                option.textContent = rank;
                rankFilter.appendChild(option);
            });
        }

        // Function to apply all filters and sorting
        function applyFiltersAndSort() {
            // First, filter the users
            const filteredUsers = users.filter(user => {
                // Search filter
                const nameMatch = `${user.firstName} ${user.lastName}`.toLowerCase().includes(currentFilters.search);
                const usernameMatch = user.username.toLowerCase().includes(currentFilters.search);
                const searchMatch = nameMatch || usernameMatch;

                // Rank filter
                const rankMatch = currentFilters.rank === 'all' || user.rankName === currentFilters.rank;

                // Status filter
                const statusMatch = currentFilters.status === 'all' || user.status === currentFilters.status;

                return searchMatch && rankMatch && statusMatch;
            });

            // Then, sort the filtered users
            filteredUsers.sort((a, b) => {
                let comparison = 0;

                switch(currentSort.column) {
                    case 'name':
                        comparison = `${a.firstName} ${a.lastName}`.localeCompare(`${b.firstName} ${b.lastName}`);
                        break;
                    case 'username':
                        comparison = a.username.localeCompare(b.username);
                        break;
                    case 'rank':
                        comparison = (a.rankName || '').localeCompare(b.rankName || '');
                        break;
                    case 'status':
                        comparison = a.status.localeCompare(b.status);
                        break;
                    default:
                        comparison = 0;
                }

                return currentSort.direction === 'asc' ? comparison : -comparison;
            });

            // Render the filtered and sorted users
            renderFilteredUsers(filteredUsers);

            // Update filter summary and result count
            updateFilterSummary(filteredUsers.length);
        }

        // Function to render filtered users
        function renderFilteredUsers(filteredUsers) {
            const tableBody = document.getElementById('userTableBody');
            tableBody.innerHTML = '';

            if (filteredUsers.length === 0) {
                const row = document.createElement('tr');
                const cell = document.createElement('td');
                cell.colSpan = 5;
                cell.className = 'px-6 py-4 text-center text-sm text-gray-500';
                cell.textContent = 'No matching users found.';
                row.appendChild(cell);
                tableBody.appendChild(row);
                return;
            }

            filteredUsers.forEach(user => {
                const row = document.createElement('tr');

                // Create name cell
                const nameCell = document.createElement('td');
                nameCell.className = 'px-6 py-4 whitespace-nowrap text-sm text-gray-900';
                nameCell.textContent = `${user.firstName} ${user.lastName}`;
                row.appendChild(nameCell);

                // Create username cell
                const usernameCell = document.createElement('td');
                usernameCell.className = 'px-6 py-4 whitespace-nowrap text-sm text-gray-900';
                usernameCell.textContent = user.username;
                row.appendChild(usernameCell);

                // Create rank cell
                const rankCell = document.createElement('td');
                rankCell.className = 'px-6 py-4 whitespace-nowrap text-sm text-gray-900';
                rankCell.textContent = user.rankName || 'Not assigned';
                row.appendChild(rankCell);

                // Create status cell with read-only status badge
                const statusCell = document.createElement('td');
                statusCell.className = 'px-6 py-4 whitespace-nowrap text-sm';

                // Create status badge
                const statusBadge = document.createElement('span');

                // Set badge color based on status
                let badgeClass = '';
                switch(user.status) {
                    case 'active':
                        badgeClass = 'bg-green-100 text-green-800';
                        break;
                    case 'pending':
                        badgeClass = 'bg-yellow-100 text-yellow-800';
                        break;
                    case 'resigned':
                        badgeClass = 'bg-gray-100 text-gray-800';
                        break;
                    case 'disabled':
                        badgeClass = 'bg-red-100 text-red-800';
                        break;
                    default:
                        badgeClass = 'bg-gray-100 text-gray-800';
                }

                statusBadge.className = `px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${badgeClass}`;

                // Capitalize first letter of status
                const displayStatus = user.status.charAt(0).toUpperCase() + user.status.slice(1);
                statusBadge.textContent = displayStatus;

                statusCell.appendChild(statusBadge);
                row.appendChild(statusCell);

                // Create actions cell
                const actionsCell = document.createElement('td');
                actionsCell.className = 'px-6 py-4 whitespace-nowrap text-sm flex space-x-4';

                const viewLink = document.createElement('a');
                viewLink.href = `details.html?user=${user.username}`;
                viewLink.style.color = '#0C423C'; // Set the exact color requested
                viewLink.style.transition = 'opacity 0.2s';
                viewLink.addEventListener('mouseenter', function() {
                    this.style.opacity = '0.8'; // Slightly fade on hover
                });
                viewLink.addEventListener('mouseleave', function() {
                    this.style.opacity = '1';
                });
                viewLink.title = 'View Details';

                // Create SVG icon for View Details (info icon)
                const infoSvg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
                infoSvg.setAttribute('width', '20');
                infoSvg.setAttribute('height', '20');
                infoSvg.setAttribute('viewBox', '0 0 24 24');
                infoSvg.setAttribute('fill', 'currentColor');
                infoSvg.setAttribute('class', 'inline-block');

                // Create path for the info icon
                const infoPath = document.createElementNS('http://www.w3.org/2000/svg', 'path');
                infoPath.setAttribute('d', 'M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z');

                infoSvg.appendChild(infoPath);
                viewLink.appendChild(infoSvg);

                actionsCell.appendChild(viewLink);

                // Add spacing
                const spacer = document.createElement('span');
                spacer.className = 'ml-4';
                actionsCell.appendChild(spacer);

                // Add modify link with icon
                const modifyLink = document.createElement('a');
                modifyLink.href = `modify-account.html?user=${user.username}`;
                modifyLink.style.color = '#0C423C'; // Set the exact color requested
                modifyLink.style.transition = 'opacity 0.2s';
                modifyLink.addEventListener('mouseenter', function() {
                    this.style.opacity = '0.8'; // Slightly fade on hover
                });
                modifyLink.addEventListener('mouseleave', function() {
                    this.style.opacity = '1';
                });
                modifyLink.title = 'Modify User';

                // Create SVG icon
                const svgIcon = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
                svgIcon.setAttribute('width', '20');
                svgIcon.setAttribute('height', '20');
                svgIcon.setAttribute('viewBox', '0 0 24 24');
                svgIcon.setAttribute('fill', 'currentColor');
                svgIcon.setAttribute('class', 'inline-block');

                // Create path for the pencil/edit icon that matches the uploaded image
                const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
                path.setAttribute('d', 'M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z');

                svgIcon.appendChild(path);
                modifyLink.appendChild(svgIcon);

                actionsCell.appendChild(modifyLink);

                // Add spacing
                const spacer2 = document.createElement('span');
                spacer2.className = 'ml-4';
                actionsCell.appendChild(spacer2);

                // Add permissions link with icon
                const permissionsLink = document.createElement('a');
                permissionsLink.href = `permissions.html?user=${user.username}`;
                permissionsLink.style.color = '#0C423C';
                permissionsLink.style.transition = 'opacity 0.2s';
                permissionsLink.addEventListener('mouseenter', function() {
                    this.style.opacity = '0.8';
                });
                permissionsLink.addEventListener('mouseleave', function() {
                    this.style.opacity = '1';
                });
                permissionsLink.title = 'View Permissions';

                // Create SVG icon for permissions
                const permSvg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
                permSvg.setAttribute('width', '20');
                permSvg.setAttribute('height', '20');
                permSvg.setAttribute('viewBox', '0 0 24 24');
                permSvg.setAttribute('fill', 'currentColor');
                permSvg.setAttribute('class', 'inline-block');

                // Create path for the lock/permissions icon
                const permPath = document.createElementNS('http://www.w3.org/2000/svg', 'path');
                permPath.setAttribute('d', 'M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2zm-6 9c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zm3.1-9H8.9V6c0-1.71 1.39-3.1 3.1-3.1 1.71 0 3.1 1.39 3.1 3.1v2z');

                permSvg.appendChild(permPath);
                permissionsLink.appendChild(permSvg);

                actionsCell.appendChild(permissionsLink);

                row.appendChild(actionsCell);

                // Add row to table
                tableBody.appendChild(row);
            });
        }

        // Function to update the filter summary and result count
        function updateFilterSummary(count) {
            const filterSummary = document.getElementById('filterSummary');
            const resultCount = document.getElementById('resultCount');

            let summaryText = 'Showing';

            if (currentFilters.search) {
                summaryText += ` search results for "${currentFilters.search}"`;
            }

            if (currentFilters.rank !== 'all') {
                summaryText += `${currentFilters.search ? ' in' : ''} rank "${currentFilters.rank}"`;
            }

            if (currentFilters.status !== 'all') {
                summaryText += `${currentFilters.search || currentFilters.rank !== 'all' ? ' with' : ''} status "${currentFilters.status}"`;
            }

            if (currentFilters.search === '' && currentFilters.rank === 'all' && currentFilters.status === 'all') {
                summaryText = 'Showing all users';
            }

            filterSummary.textContent = summaryText;
            resultCount.textContent = `${count} ${count === 1 ? 'result' : 'results'}`;
        }

        // Function to update sort indicators
        function updateSortIndicators() {
            // Hide all sort indicators
            document.querySelectorAll('.sort-icon').forEach(icon => {
                icon.classList.add('invisible');
            });

            // Show the current sort indicator
            const th = document.querySelector(`th[data-sort="${currentSort.column}"]`);
            if (th) {
                const icon = th.querySelector('.sort-icon');
                icon.textContent = currentSort.direction === 'asc' ? '▲' : '▼';
                icon.classList.remove('invisible');
            }
        }

        // Initialize the page
        populateRankFilter();
        applyFiltersAndSort();
        updateSortIndicators();

        // Add event listeners for table header sorting
        document.querySelectorAll('th[data-sort]').forEach(th => {
            th.addEventListener('click', function() {
                const column = this.getAttribute('data-sort');

                // Toggle direction if clicking the same column
                if (currentSort.column === column) {
                    currentSort.direction = currentSort.direction === 'asc' ? 'desc' : 'asc';
                } else {
                    currentSort.column = column;
                    currentSort.direction = 'asc';
                }

                updateSortIndicators();
                applyFiltersAndSort();
            });
        });

        // Search functionality
        const searchInput = document.getElementById('search');
        searchInput.addEventListener('input', function() {
            currentFilters.search = this.value.trim().toLowerCase();
            applyFiltersAndSort();
        });

        // Rank filter functionality
        const rankFilter = document.getElementById('rankFilter');
        rankFilter.addEventListener('change', function() {
            currentFilters.rank = this.value;
            applyFiltersAndSort();
        });

        // Status filter functionality
        const statusFilter = document.getElementById('statusFilter');
        statusFilter.addEventListener('change', function() {
            currentFilters.status = this.value;
            applyFiltersAndSort();
        });

        // Clear filters button
        document.getElementById('clearFilters').addEventListener('click', function() {
            // Reset filters
            currentFilters = { search: '', rank: 'all', status: 'all' };

            // Reset form elements
            document.getElementById('search').value = '';
            document.getElementById('rankFilter').value = 'all';
            document.getElementById('statusFilter').value = 'all';

            // Apply filters (which will show all users)
            applyFiltersAndSort();
        });

        // Global variables for supervisor management
        let appraisingOfficers = [];
        let countersigningOfficers = [];

        // Function to initialize supervisor management
        function initSupervisorManagement() {
            // Load supervisor lists from localStorage
            loadSupervisorLists();

            // Add event listener to the Manage Supervisor button
            document.getElementById('manageSupervisorBtn').addEventListener('click', function() {
                openSupervisorModal();
            });

            // Add event listener to close the modal
            document.getElementById('closeSupervisorModal').addEventListener('click', function() {
                document.getElementById('supervisorModal').classList.add('hidden');
            });

            // Add event listeners for adding officers
            document.getElementById('addAppraisingOfficerBtn').addEventListener('click', function() {
                addAppraisingOfficer();
            });

            document.getElementById('addCountersigningOfficerBtn').addEventListener('click', function() {
                addCountersigningOfficer();
            });

            // Add event listener for saving changes
            document.getElementById('saveSupervisorChanges').addEventListener('click', function() {
                saveSupervisorLists();
                document.getElementById('supervisorModal').classList.add('hidden');
            });

            // Populate the add officer dropdowns
            populateOfficerAddDropdowns();
        }

        // Function to load supervisor lists from localStorage
        function loadSupervisorLists() {
            const supervisorLists = JSON.parse(localStorage.getItem('supervisorLists')) || {
                appraisingOfficers: [],
                countersigningOfficers: []
            };

            appraisingOfficers = supervisorLists.appraisingOfficers;
            countersigningOfficers = supervisorLists.countersigningOfficers;

            // Display the lists
            displayAppraisingOfficers();
            displayCountersigningOfficers();
        }

        // Function to save supervisor lists to localStorage
        function saveSupervisorLists() {
            const supervisorLists = {
                appraisingOfficers: appraisingOfficers,
                countersigningOfficers: countersigningOfficers
            };

            localStorage.setItem('supervisorLists', JSON.stringify(supervisorLists));
        }

        // Function to open the supervisor management modal
        function openSupervisorModal() {
            // Refresh the lists
            displayAppraisingOfficers();
            displayCountersigningOfficers();

            // Show the modal
            document.getElementById('supervisorModal').classList.remove('hidden');
        }

        // Function to populate the officer add dropdowns
        function populateOfficerAddDropdowns() {
            const appraisingDropdown = document.getElementById('appraisingOfficerAdd');
            const countersigningDropdown = document.getElementById('countersigningOfficerAdd');

            // Clear existing options except the first one
            while (appraisingDropdown.options.length > 1) {
                appraisingDropdown.remove(1);
            }

            while (countersigningDropdown.options.length > 1) {
                countersigningDropdown.remove(1);
            }

            // Add options for each active user
            users.forEach(user => {
                if (user.status === 'active') {
                    // Create option for appraising officer dropdown
                    const appraisingOption = document.createElement('option');
                    appraisingOption.value = user.username;
                    appraisingOption.textContent = `${user.firstName} ${user.lastName} (${user.rankName || 'No Rank'})`;
                    appraisingDropdown.appendChild(appraisingOption);

                    // Create option for countersigning officer dropdown
                    const countersigningOption = document.createElement('option');
                    countersigningOption.value = user.username;
                    countersigningOption.textContent = `${user.firstName} ${user.lastName} (${user.rankName || 'No Rank'})`;
                    countersigningDropdown.appendChild(countersigningOption);
                }
            });
        }

        // Function to add an appraising officer
        function addAppraisingOfficer() {
            const dropdown = document.getElementById('appraisingOfficerAdd');
            const username = dropdown.value;

            if (!username) {
                alert('Please select a user to add as an Appraising Officer.');
                return;
            }

            // Check if already in the list
            if (appraisingOfficers.includes(username)) {
                alert('This user is already in the Appraising Officer list.');
                return;
            }

            // Add to the list
            appraisingOfficers.push(username);

            // Update the display
            displayAppraisingOfficers();

            // Reset the dropdown
            dropdown.value = '';
        }

        // Function to add a countersigning officer
        function addCountersigningOfficer() {
            const dropdown = document.getElementById('countersigningOfficerAdd');
            const username = dropdown.value;

            if (!username) {
                alert('Please select a user to add as a Countersigning Officer.');
                return;
            }

            // Check if already in the list
            if (countersigningOfficers.includes(username)) {
                alert('This user is already in the Countersigning Officer list.');
                return;
            }

            // Add to the list
            countersigningOfficers.push(username);

            // Update the display
            displayCountersigningOfficers();

            // Reset the dropdown
            dropdown.value = '';
        }

        // Function to remove an appraising officer
        function removeAppraisingOfficer(username) {
            appraisingOfficers = appraisingOfficers.filter(officer => officer !== username);
            displayAppraisingOfficers();
        }

        // Function to remove a countersigning officer
        function removeCountersigningOfficer(username) {
            countersigningOfficers = countersigningOfficers.filter(officer => officer !== username);
            displayCountersigningOfficers();
        }

        // Function to display appraising officers
        function displayAppraisingOfficers() {
            const tableBody = document.getElementById('appraisingOfficerList');

            if (appraisingOfficers.length === 0) {
                tableBody.innerHTML = `
                    <tr>
                        <td colspan="3" class="px-6 py-4 text-center text-sm text-gray-500">No officers added yet.</td>
                    </tr>
                `;
                return;
            }

            // Clear table
            tableBody.innerHTML = '';

            // Add rows for each officer
            appraisingOfficers.forEach(username => {
                const user = users.find(u => u.username === username);
                if (user) {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${user.firstName} ${user.lastName}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${user.rankName || 'No Rank'}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <button type="button" class="text-red-600 hover:text-red-900 removeAppraisingBtn" data-username="${username}">
                                Remove
                            </button>
                        </td>
                    `;
                    tableBody.appendChild(row);
                }
            });

            // Add event listeners to remove buttons
            document.querySelectorAll('.removeAppraisingBtn').forEach(button => {
                button.addEventListener('click', function() {
                    const username = this.getAttribute('data-username');
                    removeAppraisingOfficer(username);
                });
            });
        }

        // Function to display countersigning officers
        function displayCountersigningOfficers() {
            const tableBody = document.getElementById('countersigningOfficerList');

            if (countersigningOfficers.length === 0) {
                tableBody.innerHTML = `
                    <tr>
                        <td colspan="3" class="px-6 py-4 text-center text-sm text-gray-500">No officers added yet.</td>
                    </tr>
                `;
                return;
            }

            // Clear table
            tableBody.innerHTML = '';

            // Add rows for each officer
            countersigningOfficers.forEach(username => {
                const user = users.find(u => u.username === username);
                if (user) {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${user.firstName} ${user.lastName}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${user.rankName || 'No Rank'}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <button type="button" class="text-red-600 hover:text-red-900 removeCountersigningBtn" data-username="${username}">
                                Remove
                            </button>
                        </td>
                    `;
                    tableBody.appendChild(row);
                }
            });

            // Add event listeners to remove buttons
            document.querySelectorAll('.removeCountersigningBtn').forEach(button => {
                button.addEventListener('click', function() {
                    const username = this.getAttribute('data-username');
                    removeCountersigningOfficer(username);
                });
            });
        }

        // Initialize supervisor management
        initSupervisorManagement();
    </script>
</body>
</html>

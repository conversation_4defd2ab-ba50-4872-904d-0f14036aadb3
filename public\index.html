<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>View User Accounts - OFCA Portal</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="css/ofca-styles.css">
    <script src="js/header.js"></script>
    <script src="js/footer.js"></script>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="p-8">
    <div class="bg-white p-8 rounded-lg shadow-lg w-full max-w-4xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold">User Account Management</h1>
            <a href="create-account.html" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700">Create Account</a>
        </div>

        <!-- Search and Filter Section -->
        <div class="mb-6">
            <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                <div class="flex-1">
                    <label for="search" class="block text-sm font-medium text-gray-700">Search Staff</label>
                    <input type="text" id="search" placeholder="Enter name or username..." class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                </div>
                <div class="flex gap-4">
                    <div>
                        <label for="sort" class="block text-sm font-medium text-gray-700">Sort By</label>
                        <select id="sort" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                            <option value="name">Name (A-Z)</option>
                            <option value="name-desc">Name (Z-A)</option>
                            <option value="department">Department</option>
                            <option value="status">Status</option>
                        </select>
                    </div>
                    <div>
                        <label for="filter" class="block text-sm font-medium text-gray-700">Filter By Status</label>
                        <select id="filter" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                            <option value="all">All</option>
                            <option value="active">Active</option>
                            <option value="pending">Pending</option>
                            <option value="resigned">Resigned</option>
                            <option value="disabled">Disabled</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- User Account List -->
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200" id="userTable">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Username</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Department</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200" id="userTableBody">
                    <!-- User rows will be dynamically generated by JavaScript -->
                </tbody>
            </table>
        </div>
    </div>

    <script>
        // Initialize default users if none exist
        const defaultUsers = [
            {
                id: '1',
                firstName: 'John',
                lastName: 'Doe',
                username: 'jdoe',
                email: '<EMAIL>',
                phone: '(*************',
                department: 'IT',
                jobTitle: 'Software Engineer',
                role: 'Admin',
                status: 'active',
                accessLevel: 'Full',
                notes: 'System administrator with full access'
            },
            {
                id: '2',
                firstName: 'Jane',
                lastName: 'Smith',
                username: 'jsmith',
                email: '<EMAIL>',
                phone: '(*************',
                department: 'HR',
                jobTitle: 'HR Manager',
                role: 'User',
                status: 'pending',
                accessLevel: 'Standard',
                notes: 'Awaiting approval from department head'
            },
            {
                id: '3',
                firstName: 'Alice',
                lastName: 'Johnson',
                username: 'ajohnson',
                email: '<EMAIL>',
                phone: '(*************',
                department: 'Finance',
                jobTitle: 'Accountant',
                role: 'User',
                status: 'resigned',
                accessLevel: 'Basic',
                notes: 'Left the company on 2023-12-15'
            },
            {
                id: '4',
                firstName: 'Robert',
                lastName: 'Williams',
                username: 'rwilliams',
                email: '<EMAIL>',
                phone: '(*************',
                department: 'Sales',
                jobTitle: 'Sales Representative',
                role: 'User',
                status: 'disabled',
                accessLevel: 'Standard',
                notes: 'Account temporarily disabled due to extended leave'
            }
        ];

        // Get users from localStorage or use defaults
        let users = JSON.parse(localStorage.getItem('users')) || defaultUsers;

        // If users array is empty, use defaults
        if (users.length === 0) {
            users = defaultUsers;
            localStorage.setItem('users', JSON.stringify(users));
        }

        // Function to render the user table
        function renderUserTable() {
            const tableBody = document.getElementById('userTableBody');
            tableBody.innerHTML = '';

            users.forEach(user => {
                const row = document.createElement('tr');

                // Create name cell
                const nameCell = document.createElement('td');
                nameCell.className = 'px-6 py-4 whitespace-nowrap text-sm text-gray-900';
                nameCell.textContent = `${user.firstName} ${user.lastName}`;
                row.appendChild(nameCell);

                // Create username cell
                const usernameCell = document.createElement('td');
                usernameCell.className = 'px-6 py-4 whitespace-nowrap text-sm text-gray-900';
                usernameCell.textContent = user.username;
                row.appendChild(usernameCell);

                // Create department cell
                const deptCell = document.createElement('td');
                deptCell.className = 'px-6 py-4 whitespace-nowrap text-sm text-gray-900';
                deptCell.textContent = user.department;
                row.appendChild(deptCell);

                // Create status cell with read-only status badge
                const statusCell = document.createElement('td');
                statusCell.className = 'px-6 py-4 whitespace-nowrap text-sm';

                // Create status badge
                const statusBadge = document.createElement('span');

                // Set badge color based on status
                let badgeClass = '';
                switch(user.status) {
                    case 'active':
                        badgeClass = 'bg-green-100 text-green-800';
                        break;
                    case 'pending':
                        badgeClass = 'bg-yellow-100 text-yellow-800';
                        break;
                    case 'resigned':
                        badgeClass = 'bg-gray-100 text-gray-800';
                        break;
                    case 'disabled':
                        badgeClass = 'bg-red-100 text-red-800';
                        break;
                    default:
                        badgeClass = 'bg-gray-100 text-gray-800';
                }

                statusBadge.className = `px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${badgeClass}`;

                // Capitalize first letter of status
                const displayStatus = user.status.charAt(0).toUpperCase() + user.status.slice(1);
                statusBadge.textContent = displayStatus;

                statusCell.appendChild(statusBadge);
                row.appendChild(statusCell);

                // Create actions cell
                const actionsCell = document.createElement('td');
                actionsCell.className = 'px-6 py-4 whitespace-nowrap text-sm flex space-x-4';

                const viewLink = document.createElement('a');
                viewLink.href = `details.html?user=${user.username}`;
                viewLink.style.color = '#0C423C'; // Set the exact color requested
                viewLink.style.transition = 'opacity 0.2s';
                viewLink.addEventListener('mouseenter', function() {
                    this.style.opacity = '0.8'; // Slightly fade on hover
                });
                viewLink.addEventListener('mouseleave', function() {
                    this.style.opacity = '1';
                });
                viewLink.title = 'View Details';

                // Create SVG icon for View Details (info icon)
                const infoSvg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
                infoSvg.setAttribute('width', '20');
                infoSvg.setAttribute('height', '20');
                infoSvg.setAttribute('viewBox', '0 0 24 24');
                infoSvg.setAttribute('fill', 'currentColor');
                infoSvg.setAttribute('class', 'inline-block');

                // Create path for the info icon
                const infoPath = document.createElementNS('http://www.w3.org/2000/svg', 'path');
                infoPath.setAttribute('d', 'M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z');

                infoSvg.appendChild(infoPath);
                viewLink.appendChild(infoSvg);

                actionsCell.appendChild(viewLink);

                // Add spacing
                const spacer = document.createElement('span');
                spacer.className = 'ml-4';
                actionsCell.appendChild(spacer);

                // Add modify link with icon
                const modifyLink = document.createElement('a');
                modifyLink.href = `modify-account.html?user=${user.username}`;
                modifyLink.style.color = '#0C423C'; // Set the exact color requested
                modifyLink.style.transition = 'opacity 0.2s';
                modifyLink.addEventListener('mouseenter', function() {
                    this.style.opacity = '0.8'; // Slightly fade on hover
                });
                modifyLink.addEventListener('mouseleave', function() {
                    this.style.opacity = '1';
                });
                modifyLink.title = 'Modify User';

                // Create SVG icon
                const svgIcon = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
                svgIcon.setAttribute('width', '20');
                svgIcon.setAttribute('height', '20');
                svgIcon.setAttribute('viewBox', '0 0 24 24');
                svgIcon.setAttribute('fill', 'currentColor');
                svgIcon.setAttribute('class', 'inline-block');

                // Create path for the pencil/edit icon that matches the uploaded image
                const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
                path.setAttribute('d', 'M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z');

                svgIcon.appendChild(path);
                modifyLink.appendChild(svgIcon);

                actionsCell.appendChild(modifyLink);

                row.appendChild(actionsCell);

                // Add row to table
                tableBody.appendChild(row);
            });
        }

        // Initial render
        renderUserTable();

        // Search functionality
        const searchInput = document.getElementById('search');
        searchInput.addEventListener('input', function () {
            const searchTerm = this.value.trim().toLowerCase();
            const rows = document.getElementById('userTableBody').getElementsByTagName('tr');

            for (let row of rows) {
                const name = row.cells[0].textContent.toLowerCase();
                const username = row.cells[1].textContent.toLowerCase();

                if (name.includes(searchTerm) || username.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            }
        });

        // Filter functionality
        const filterSelect = document.getElementById('filter');
        filterSelect.addEventListener('change', function() {
            const filterValue = this.value;
            const rows = document.getElementById('userTableBody').getElementsByTagName('tr');

            for (let i = 0; i < rows.length; i++) {
                const row = rows[i];
                // Get the user object for this row
                const user = users[i];
                const status = user.status;

                if (filterValue === 'all' || status === filterValue) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            }
        });

        // Sort functionality
        const sortSelect = document.getElementById('sort');
        sortSelect.addEventListener('change', function() {
            const sortValue = this.value;

            // Sort users array based on selected option
            users.sort((a, b) => {
                switch(sortValue) {
                    case 'name':
                        return `${a.firstName} ${a.lastName}`.localeCompare(`${b.firstName} ${b.lastName}`);
                    case 'name-desc':
                        return `${b.firstName} ${b.lastName}`.localeCompare(`${a.firstName} ${a.lastName}`);
                    case 'department':
                        return a.department.localeCompare(b.department);
                    case 'status':
                        return a.status.localeCompare(b.status);
                    default:
                        return 0;
                }
            });

            // Re-render table with sorted data
            renderUserTable();
        });
    </script>
</body>
</html>
